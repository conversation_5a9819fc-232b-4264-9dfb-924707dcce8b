import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import '../constants/app_colors.dart';
import '../utils/language_manager.dart';
import '../../shared/services/real_data_service.dart';
import '../../shared/models/driver.dart';

/// خدمة فتح موقع السائق في الخرائط الخارجية
class DriverLocationService {
  
  /// فتح موقع السائق الحقيقي في الخرائط الخارجية
  static Future<void> openDriverRealLocation({
    required BuildContext context,
    required String driverId,
    String? driverName,
    bool showLoadingDialog = true,
  }) async {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    
    if (showLoadingDialog) {
      // عرض dialog التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => _buildLoadingDialog(langManager),
      );
    }
    
    try {
      // الحصول على موقع السائق الحقيقي من قاعدة البيانات
      final dataService = Provider.of<RealDataService>(context, listen: false);
      final drivers = await dataService.getAvailableDrivers();
      final driver = drivers.firstWhere(
        (d) => d.id == driverId,
        orElse: () => throw Exception('السائق غير موجود'),
      );
      
      if (showLoadingDialog) {
        Navigator.pop(context); // إغلاق dialog التحميل
      }
      
      if (driver.currentLocation != null) {
        // فتح الموقع الحقيقي
        await _openLocationInMaps(
          context: context,
          latitude: driver.currentLocation!.latitude,
          longitude: driver.currentLocation!.longitude,
          label: driverName ?? driver.name,
        );
      } else {
        // السائق لا يوجد له موقع محدث
        _showErrorMessage(
          context,
          langManager.getText(
            'موقع السائق غير متاح حالياً',
            'Driver location is not available',
          ),
        );
      }
    } catch (e) {
      if (showLoadingDialog && Navigator.canPop(context)) {
        Navigator.pop(context); // إغلاق dialog التحميل
      }
      
      _showErrorMessage(
        context,
        langManager.getText(
          'خطأ في الحصول على موقع السائق: $e',
          'Error getting driver location: $e',
        ),
      );
    }
  }
  
  /// فتح موقع محدد في الخرائط الخارجية
  static Future<void> openLocationInMaps({
    required BuildContext context,
    required double latitude,
    required double longitude,
    String? label,
  }) async {
    await _openLocationInMaps(
      context: context,
      latitude: latitude,
      longitude: longitude,
      label: label,
    );
  }
  
  /// فتح الموقع في تطبيقات الخرائط
  static Future<void> _openLocationInMaps({
    required BuildContext context,
    required double latitude,
    required double longitude,
    String? label,
  }) async {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    
    try {
      // رابط Google Maps لعرض الموقع
      String googleMapsUrl = 'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
      if (label != null) {
        googleMapsUrl += '&query_place_id=${Uri.encodeComponent(label)}';
      }
      
      bool canLaunch = await canLaunchUrl(Uri.parse(googleMapsUrl));
      if (canLaunch) {
        await launchUrl(Uri.parse(googleMapsUrl), mode: LaunchMode.externalApplication);
      } else {
        // محاولة فتح تطبيق خرائط آخر
        String fallbackUrl = 'geo:$latitude,$longitude';
        if (label != null) {
          fallbackUrl += '?q=$latitude,$longitude(${Uri.encodeComponent(label)})';
        }
        
        if (await canLaunchUrl(Uri.parse(fallbackUrl))) {
          await launchUrl(Uri.parse(fallbackUrl), mode: LaunchMode.externalApplication);
        } else {
          _showErrorMessage(
            context,
            langManager.getText('لا يمكن فتح تطبيق الخرائط', 'Cannot open maps application'),
          );
        }
      }
    } catch (e) {
      _showErrorMessage(
        context,
        langManager.getText('خطأ في فتح الخرائط', 'Error opening maps'),
      );
    }
  }
  
  /// عرض dialog التحميل
  static Widget _buildLoadingDialog(LanguageManager langManager) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(color: AppColors.primary),
            const SizedBox(height: 16),
            Text(
              langManager.getText('جاري تحديد موقع السائق...', 'Locating driver...'),
              style: GoogleFonts.tajawal(
                fontSize: 16,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
  
  /// عرض رسالة خطأ
  static void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.tajawal(),
        ),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
  
  /// عرض رسالة نجاح
  static void _showSuccessMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.tajawal(),
        ),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
  
  /// فتح الملاحة من موقع السائق إلى وجهة محددة
  static Future<void> openNavigationFromDriverToDestination({
    required BuildContext context,
    required String driverId,
    required double destinationLatitude,
    required double destinationLongitude,
    String? destinationLabel,
    String? driverName,
  }) async {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    
    // عرض dialog التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildLoadingDialog(langManager),
    );
    
    try {
      // الحصول على موقع السائق الحقيقي
      final dataService = Provider.of<RealDataService>(context, listen: false);
      final drivers = await dataService.getAvailableDrivers();
      final driver = drivers.firstWhere(
        (d) => d.id == driverId,
        orElse: () => throw Exception('السائق غير موجود'),
      );
      
      Navigator.pop(context); // إغلاق dialog التحميل
      
      if (driver.currentLocation != null) {
        // فتح الملاحة من موقع السائق إلى الوجهة
        String googleMapsUrl = 'https://www.google.com/maps/dir/${driver.currentLocation!.latitude},${driver.currentLocation!.longitude}/$destinationLatitude,$destinationLongitude';
        
        bool canLaunch = await canLaunchUrl(Uri.parse(googleMapsUrl));
        if (canLaunch) {
          await launchUrl(Uri.parse(googleMapsUrl), mode: LaunchMode.externalApplication);
        } else {
          _showErrorMessage(
            context,
            langManager.getText('لا يمكن فتح تطبيق الخرائط', 'Cannot open maps application'),
          );
        }
      } else {
        _showErrorMessage(
          context,
          langManager.getText(
            'موقع السائق غير متاح حالياً',
            'Driver location is not available',
          ),
        );
      }
    } catch (e) {
      if (Navigator.canPop(context)) {
        Navigator.pop(context); // إغلاق dialog التحميل
      }
      
      _showErrorMessage(
        context,
        langManager.getText(
          'خطأ في الحصول على موقع السائق: $e',
          'Error getting driver location: $e',
        ),
      );
    }
  }
}
