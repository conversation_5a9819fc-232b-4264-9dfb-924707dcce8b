import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:icons_plus/icons_plus.dart';
import 'dart:math' as math;

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/utils/language_manager.dart';
import '../../shared/services/notification_service.dart';
import '../../shared/services/real_data_service.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();

  // دالة ثابتة لإكمال الطلب من شاشات أخرى
  static void completeCurrentTrip() {
    // سيتم استدعاؤها من شاشة الخريطة
    // هذه الدالة ستقوم بنقل الطلب النشط إلى المكتملة
  }
}

class _OrdersScreenState extends State<OrdersScreen>
    with TickerProviderStateMixin {

  // ═══════════════════════════════════════════════════════════════
  // متحكمات الأنيميشن (Animation Controllers)
  // ═══════════════════════════════════════════════════════════════

  /// متحكم أنيميشن الظهور التدريجي للعناصر
  late AnimationController _fadeController;

  /// متحكم أنيميشن الخلفية المتحركة
  late AnimationController _backgroundController;

  /// متحكم أنيميشن ظهور الطلبات
  late AnimationController _ordersController;

  // ═══════════════════════════════════════════════════════════════
  // خدمات البيانات والإشعارات (Services)
  // ═══════════════════════════════════════════════════════════════

  /// خدمة الإشعارات
  late NotificationService _notificationService;

  /// خدمة البيانات الحقيقية
  late RealDataService _dataService;

  // ═══════════════════════════════════════════════════════════════
  // كائنات الأنيميشن (Animation Objects)
  // ═══════════════════════════════════════════════════════════════

  /// أنيميشن الظهور التدريجي
  late Animation<double> _fadeAnimation;

  /// أنيميشن الخلفية المتحركة
  late Animation<double> _backgroundAnimation;

  /// أنيميشن ظهور الطلبات
  late Animation<double> _ordersAnimation;

  // ═══════════════════════════════════════════════════════════════
  // متغيرات حالة الشاشة (Screen State Variables)
  // ═══════════════════════════════════════════════════════════════

  /// فهرس التبويب النشط (0: متاحة، 1: قيد التنفيذ، 2: مكتملة)
  int _selectedTabIndex = 0;

  /// حالة التحديث
  bool _isRefreshing = false;

  /// حالة وجود طلب نشط
  bool _hasActiveOrder = false;

  /// معرف الطلب النشط الحالي
  String? _currentActiveOrderId;

  /// فلتر المسافة المحدد
  String _selectedDistanceFilter = 'الكل';

  /// فلتر السعر المحدد
  String _selectedPriceFilter = 'الكل';

  // ═══════════════════════════════════════════════════════════════
  // بيانات وهمية للطلبات (Mock Orders Data)
  // ═══════════════════════════════════════════════════════════════

  /// قائمة الطلبات المتاحة
  final List<Map<String, dynamic>> _availableOrders = [
    {
      'id': '001',
      'from': 'شارع الرشيد',
      'fromEn': 'Al-Rashid Street',
      'to': 'مدينة الصدر',
      'toEn': 'Sadr City',
      'distance': '12.5 km',
      'duration': '25 min',
      'price': 25.00,
      'customerName': 'أحمد محمد',
      'customerNameEn': 'Ahmed Mohammed',
      'customerRating': 4.8,
      'orderTime': '10:30 AM',
      'priority': 'عادي',
      'priorityEn': 'Normal',
    },
    {
      'id': '002',
      'from': 'الكرادة',
      'fromEn': 'Al-Karrada',
      'to': 'الجادرية',
      'toEn': 'Al-Jadriya',
      'distance': '8.2 km',
      'duration': '18 min',
      'price': 18.50,
      'customerName': 'فاطمة علي',
      'customerNameEn': 'Fatima Ali',
      'customerRating': 4.9,
      'orderTime': '10:45 AM',
      'priority': 'عاجل',
      'priorityEn': 'Urgent',
    },
    {
      'id': '003',
      'from': 'المنصور',
      'fromEn': 'Al-Mansour',
      'to': 'الحرية',
      'toEn': 'Al-Hurriya',
      'distance': '15.3 km',
      'duration': '32 min',
      'price': 32.00,
      'customerName': 'محمد حسن',
      'customerNameEn': 'Mohammed Hassan',
      'customerRating': 4.7,
      'orderTime': '11:00 AM',
      'priority': 'عادي',
      'priorityEn': 'Normal',
    },
  ];

  /// قائمة الطلبات قيد التنفيذ
  final List<Map<String, dynamic>> _activeOrders = [
    {
      'id': '004',
      'from': 'الزعفرانية',
      'fromEn': 'Al-Zafaraniya',
      'to': 'بغداد الجديدة',
      'toEn': 'New Baghdad',
      'distance': '20.1 km',
      'duration': '45 min',
      'price': 40.00,
      'customerName': 'سارة أحمد',
      'customerNameEn': 'Sara Ahmed',
      'customerRating': 4.6,
      'startTime': '09:30 AM',
      'estimatedArrival': '10:15 AM',
      'status': 'في الطريق',
      'statusEn': 'On the way',
    },
  ];

  /// قائمة الطلبات المكتملة
  final List<Map<String, dynamic>> _completedOrders = [
    {
      'id': '005',
      'from': 'الأعظمية',
      'fromEn': 'Al-Adhamiya',
      'to': 'الكاظمية',
      'toEn': 'Al-Kadhimiya',
      'distance': '14.7 km',
      'duration': '28 min',
      'price': 28.00,
      'customerName': 'علي حسين',
      'customerNameEn': 'Ali Hussein',
      'customerRating': 4.9,
      'completedTime': '09:00 AM',
      'earnings': 28.00,
      'rating': 5.0,
    },
    {
      'id': '006',
      'from': 'الدورة',
      'fromEn': 'Al-Dora',
      'to': 'الشعلة',
      'toEn': 'Al-Shaala',
      'distance': '11.2 km',
      'duration': '22 min',
      'price': 22.00,
      'customerName': 'زينب محمد',
      'customerNameEn': 'Zeinab Mohammed',
      'customerRating': 4.8,
      'completedTime': '08:30 AM',
      'earnings': 22.00,
      'rating': 4.8,
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeAnimations();
    _startAnimations();
  }

  /// تهيئة الخدمات
  void _initializeServices() {
    _notificationService = NotificationService();
    _dataService = RealDataService();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // التحقق من وجود معاملات من الشاشة السابقة
    final arguments = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      // تعيين التبويب المحدد
      if (arguments['selectedTab'] != null) {
        setState(() {
          _selectedTabIndex = arguments['selectedTab'] as int;
        });
      }

      // إكمال الطلب النشط وإزالته من القائمة النشطة
      if (arguments['completeActiveTrip'] == true) {
        setState(() {
          // إزالة الطلب النشط إذا كان موجوداً
          if (_activeOrders.isNotEmpty) {
            _activeOrders.clear();
          }

          // إعادة تعيين حالة الطلب النشط
          _hasActiveOrder = false;
          _currentActiveOrderId = null;
        });
      }

      // إضافة طلب مكتمل جديد إذا تم تمريره
      if (arguments['completedTrip'] != null) {
        final completedTrip = arguments['completedTrip'] as Map<String, dynamic>;
        setState(() {
          _completedOrders.insert(0, completedTrip);
        });

        // عرض رسالة نجاح
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _showSuccessMessage('تم إكمال الرحلة بنجاح!', 'Trip completed successfully!');
          }
        });
      }
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // دالة تهيئة جميع الأنيميشن (Initialize All Animations)
  // ═══════════════════════════════════════════════════════════════
  void _initializeAnimations() {

    // أنيميشن الظهور التدريجي
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // أنيميشن الخلفية المتحركة
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    // أنيميشن ظهور الطلبات
    _ordersController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // إنشاء كائنات الأنيميشن
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.linear,
    ));

    _ordersAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _ordersController,
      curve: Curves.elasticOut,
    ));
  }

  // ═══════════════════════════════════════════════════════════════
  // دالة بدء جميع الأنيميشن (Start All Animations)
  // ═══════════════════════════════════════════════════════════════
  void _startAnimations() {
    _fadeController.forward();
    // إيقاف أنيميشن الخلفية المتكرر
    // _backgroundController.repeat();

    // عرض الطلبات مباشرة
    _ordersController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _backgroundController.dispose();
    _ordersController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // الخلفية الثابتة
          _buildStaticBackground(),

              // المحتوى الرئيسي
              SafeArea(
                child: Column(
                  children: [
                    // شريط التنقل العلوي
                    _buildAppBar(),

                    // التبويبات
                    _buildTabBar(),

                    // المحتوى حسب التبويب المحدد
                    Expanded(
                      child: _buildTabContent(),
                    ),
                  ],
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildStaticBackground() {
    return Positioned.fill(
      child: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.enhancedBackgroundGradient,
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Container(
            padding: const EdgeInsets.all(20.0),
            child: Row(
              children: [
                // زر الرجوع
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: const Icon(
                      Bootstrap.arrow_left,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // العنوان
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        langManager.getText('الطلبات', 'Orders'),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        langManager.getText('إدارة طلبات النقل', 'Manage Transport Orders'),
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),

                // أيقونات الإجراءات
                Row(
                  children: [
                    _buildActionIcon(
                      icon: Bootstrap.funnel,
                      onTap: () => _showFilters(),
                    ),
                    const SizedBox(width: 12),
                    _buildActionIcon(
                      icon: Bootstrap.arrow_clockwise,
                      onTap: () => _refreshOrders(),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionIcon({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                _buildTabItem(
                  index: 0,
                  title: langManager.getText('متاحة', 'Available'),
                  count: _availableOrders.length,
                ),
                _buildTabItem(
                  index: 1,
                  title: langManager.getText('مكتملة', 'Completed'),
                  count: _completedOrders.length,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTabItem({
    required int index,
    required String title,
    required int count,
  }) {
    final isSelected = _selectedTabIndex == index;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedTabIndex = index;
          });
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
              ? Colors.white.withOpacity(0.25)
              : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            boxShadow: isSelected ? [
              BoxShadow(
                color: Colors.white.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ] : null,
          ),
          child: Column(
            children: [
              Text(
                title,
                style: TextStyle(
                  color: isSelected
                    ? Colors.white
                    : Colors.white.withOpacity(0.7),
                  fontSize: 14,
                  fontWeight: isSelected
                    ? FontWeight.w600
                    : FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: isSelected
                    ? Colors.white.withOpacity(0.3)
                    : Colors.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  count.toString(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFilters() {
    // TODO: تنفيذ عرض الفلاتر
  }

  void _refreshOrders() {
    setState(() {
      _isRefreshing = true;
    });

    // محاكاة تحديث البيانات
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    });
  }

  Widget _buildTabContent() {
    if (_isRefreshing) {
      return _buildLoadingIndicator();
    }

    switch (_selectedTabIndex) {
      case 0:
        return _buildAvailableOrders();
      case 1:
        return _buildCompletedOrders();
      default:
        return _buildAvailableOrders();
    }
  }

  Widget _buildLoadingIndicator() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(30),
            ),
            child: const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              strokeWidth: 3,
            ),
          ),
          const SizedBox(height: 16),
          Consumer<LanguageManager>(
            builder: (context, langManager, child) {
              return Text(
                langManager.getText('جاري التحديث...', 'Refreshing...'),
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 16,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAvailableOrders() {
    return AnimatedBuilder(
      animation: _ordersAnimation,
      builder: (context, child) {
        return ListView.builder(
          padding: const EdgeInsets.all(12),
          itemCount: _availableOrders.length,
          itemBuilder: (context, index) {
            final order = _availableOrders[index];
            final delay = index * 0.1;
            final animationValue = math.max(0.0, _ordersAnimation.value - delay).clamp(0.0, 1.0);

            return Transform.translate(
              offset: Offset(0, 50 * (1 - animationValue)),
              child: FadeTransition(
                opacity: AlwaysStoppedAnimation(animationValue),
                child: Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: _buildAvailableOrderCard(order),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildAvailableOrderCard(Map<String, dynamic> order) {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.2),
                Colors.white.withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            children: [
              // رأس البطاقة
              Row(
                children: [
                  // أيقونة الأولوية
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: order['priority'] == 'عاجل'
                        ? AppColors.error.withOpacity(0.2)
                        : AppColors.success.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: order['priority'] == 'عاجل'
                          ? AppColors.error.withOpacity(0.5)
                          : AppColors.success.withOpacity(0.5),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      langManager.getText(order['priority'], order['priorityEn']),
                      style: TextStyle(
                        color: order['priority'] == 'عاجل'
                          ? AppColors.error
                          : AppColors.success,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  const Spacer(),

                  // وقت الطلب
                  Row(
                    children: [
                      Icon(
                        Bootstrap.clock,
                        color: Colors.white.withOpacity(0.7),
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        order['orderTime'],
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // معلومات الطلب المخفية
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.info.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.info.withOpacity(0.3)),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(Bootstrap.geo_alt, color: AppColors.info, size: 20),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            langManager.getText('طلب نقل متاح', 'Transport Request Available'),
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12),
                    Row(
                      children: [
                        Icon(Bootstrap.arrow_right, color: Colors.white.withOpacity(0.7), size: 16),
                        SizedBox(width: 8),
                        Text(
                          langManager.getText('المسافة: ', 'Distance: ') + order['distance'],
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.warning.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Bootstrap.lock, color: AppColors.warning, size: 16),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              langManager.getText('تفاصيل الموقع والشركة ستظهر بعد الدفع', 'Location and company details will show after payment'),
                              style: TextStyle(
                                color: AppColors.warning,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // معلومات الرحلة
              Row(
                children: [
                  _buildTripInfo(
                    icon: Bootstrap.clock,
                    label: langManager.getText('المدة', 'Duration'),
                    value: order['duration'],
                  ),
                  const Spacer(),
                  _buildPriceInfo(order['price']),
                ],
              ),

              const SizedBox(height: 20),

              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: _buildActionButton(
                      title: langManager.getText('رفض', 'Decline'),
                      color: AppColors.error,
                      onTap: () => _declineOrder(order['id']),
                      isOutlined: true,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: _buildActionButton(
                      title: langManager.getText('قبول الطلب', 'Accept Order'),
                      color: AppColors.success,
                      onTap: () => _acceptOrder(order['id']),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildRouteInfo(Map<String, dynamic> order, LanguageManager langManager) {
    return Column(
      children: [
        // نقطة البداية
        Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: const BoxDecoration(
                color: AppColors.success,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                langManager.getText(order['from'], order['fromEn']),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),

        // خط الاتصال
        Container(
          margin: const EdgeInsets.only(left: 6, top: 8, bottom: 8),
          child: Row(
            children: [
              Container(
                width: 2,
                height: 30,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.success.withOpacity(0.8),
                      AppColors.error.withOpacity(0.8),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // نقطة النهاية
        Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: const BoxDecoration(
                color: AppColors.error,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                langManager.getText(order['to'], order['toEn']),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTripInfo({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: Colors.white.withOpacity(0.7),
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 12,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildPriceInfo(dynamic price) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.success.withOpacity(0.3),
            AppColors.success.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.success.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            price is double ? '\$${price.toStringAsFixed(2)}' : price.toString(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Consumer<LanguageManager>(
            builder: (context, langManager, child) {
              return Text(
                langManager.getText('السعر', 'Price'),
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 12,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String title,
    required Color color,
    required VoidCallback onTap,
    bool isOutlined = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 14),
        decoration: BoxDecoration(
          color: isOutlined ? Colors.transparent : color,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color,
            width: 2,
          ),
        ),
        child: Text(
          title,
          style: TextStyle(
            color: isOutlined ? color : Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildActiveOrders() {
    return AnimatedBuilder(
      animation: _ordersAnimation,
      builder: (context, child) {
        if (_activeOrders.isEmpty) {
          return _buildEmptyState('لا توجد طلبات نشطة', 'No active orders', Bootstrap.truck);
        }

        return ListView.builder(
          padding: const EdgeInsets.all(12),
          itemCount: _activeOrders.length,
          itemBuilder: (context, index) {
            final order = _activeOrders[index];
            final delay = index * 0.1;
            final animationValue = math.max(0.0, _ordersAnimation.value - delay).clamp(0.0, 1.0);

            return Transform.translate(
              offset: Offset(0, 50 * (1 - animationValue)),
              child: FadeTransition(
                opacity: AlwaysStoppedAnimation(animationValue),
                child: Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: _buildActiveOrderCard(order),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildCompletedOrders() {
    return AnimatedBuilder(
      animation: _ordersAnimation,
      builder: (context, child) {
        if (_completedOrders.isEmpty) {
          return _buildEmptyState('لا توجد طلبات مكتملة', 'No completed orders', Bootstrap.check_circle);
        }

        return ListView.builder(
          padding: const EdgeInsets.all(12),
          itemCount: _completedOrders.length,
          itemBuilder: (context, index) {
            final order = _completedOrders[index];
            final delay = index * 0.1;
            final animationValue = math.max(0.0, _ordersAnimation.value - delay).clamp(0.0, 1.0);

            return Transform.translate(
              offset: Offset(0, 50 * (1 - animationValue)),
              child: FadeTransition(
                opacity: AlwaysStoppedAnimation(animationValue),
                child: Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: _buildCompletedOrderCard(order),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _acceptOrder(String orderId) {
    // التحقق من وجود طلب نشط
    if (_hasActiveOrder) {
      _showSingleOrderWarning();
      return;
    }

    // عرض نافذة تأكيد الدفع
    _showOrderConfirmationDialog(orderId);
  }

  void _showSingleOrderWarning() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تنبيه'),
        content: Text('يمكنك قبول طلب واحد فقط في نفس الوقت. يرجى إنهاء الطلب الحالي أولاً.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showOrderConfirmationDialog(String orderId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد قبول الطلب',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'سيتم استقطاع 5000 دينار عراقي من رصيدك عند قبول هذا الطلب.',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.warning.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.warning.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Bootstrap.exclamation_triangle, color: AppColors.warning, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'تفاصيل الطلب ستظهر بعد الموافقة والدفع',
                      style: TextStyle(
                        color: AppColors.warning,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processOrderAcceptance(orderId);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: Text('موافق - ادفع واقبل'),
          ),
        ],
      ),
    );
  }

  void _processOrderAcceptance(String orderId) {
    // محاكاة معالجة الدفع
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري معالجة الدفع...'),
          ],
        ),
      ),
    );

    // محاكاة تأخير المعالجة
    Future.delayed(Duration(seconds: 2), () {
      Navigator.pop(context); // إغلاق نافذة التحميل

      // معالجة قبول الطلب
      _finalizeOrderAcceptance(orderId);
    });
  }

  void _finalizeOrderAcceptance(String orderId) async {
    try {
      // البحث عن الطلب في القائمة المتاحة
      final orderIndex = _availableOrders.indexWhere((order) => order['id'] == orderId);
      if (orderIndex != -1) {
        final order = _availableOrders[orderIndex];

        // إرسال إشعار للشركة بقبول الطلب مع البيانات الحقيقية
        await _sendOrderAcceptanceNotification(order);

        setState(() {
          // تعيين الطلب كنشط
          _hasActiveOrder = true;
          _currentActiveOrderId = orderId;

          // إضافة الطلب للطلبات النشطة
          _activeOrders.add({
            ...order,
            'startTime': '${DateTime.now().hour}:${DateTime.now().minute.toString().padLeft(2, '0')}',
            'estimatedArrival': '${DateTime.now().add(Duration(minutes: 30)).hour}:${DateTime.now().add(Duration(minutes: 30)).minute.toString().padLeft(2, '0')}',
            'status': 'في الطريق',
            'statusEn': 'On the way',
          });

          // إزالة الطلب من القائمة المتاحة
          _availableOrders.removeAt(orderIndex);
        });

        // عرض تفاصيل الطلب بعد الدفع
        _showOrderDetails(order);
      }
    } catch (e) {
      print('خطأ في إرسال إشعار قبول الطلب: $e');
      // المتابعة حتى لو فشل الإشعار
      setState(() {
        final orderIndex = _availableOrders.indexWhere((order) => order['id'] == orderId);
        if (orderIndex != -1) {
          final order = _availableOrders[orderIndex];
          _hasActiveOrder = true;
          _currentActiveOrderId = orderId;
          _activeOrders.add({
            ...order,
            'startTime': '${DateTime.now().hour}:${DateTime.now().minute.toString().padLeft(2, '0')}',
            'estimatedArrival': '${DateTime.now().add(Duration(minutes: 30)).hour}:${DateTime.now().add(Duration(minutes: 30)).minute.toString().padLeft(2, '0')}',
            'status': 'في الطريق',
            'statusEn': 'On the way',
          });
          _availableOrders.removeAt(orderIndex);
          _showOrderDetails(order);
        }
      });
    }
  }

  /// إرسال إشعار للشركة بقبول الطلب مع البيانات الحقيقية
  Future<void> _sendOrderAcceptanceNotification(Map<String, dynamic> order) async {
    try {
      // بيانات السائق الحقيقية (يمكن الحصول عليها من المستخدم الحالي)
      const driverData = {
        'id': 'driver_001',
        'name': 'أحمد محمد الكريم',
        'phone': '+*********** 4567',
        'latitude': 33.3152,
        'longitude': 44.3661,
        'location': 'منطقة الكرادة - شارع الكندي',
        'vehicleType': 'شاحنة كبيرة',
        'plateNumber': 'بغداد 12345',
      };

      // إرسال الإشعار للشركة
      await _notificationService.notifyCompanyOfOrderAcceptance(
        companyId: 'company_001', // معرف الشركة (يجب الحصول عليه من الطلب)
        orderId: order['id'],
        driverName: driverData['name']!,
        driverId: driverData['id']!,
        driverPhone: driverData['phone'],
        driverLatitude: driverData['latitude'] as double,
        driverLongitude: driverData['longitude'] as double,
        driverLocation: driverData['location'],
        vehicleType: driverData['vehicleType'],
        plateNumber: driverData['plateNumber'],
      );

      print('✅ تم إرسال إشعار قبول الطلب للشركة بنجاح');

    } catch (e) {
      print('❌ خطأ في إرسال إشعار قبول الطلب: $e');
      throw e;
    }
  }

  void _showOrderDetails(Map<String, dynamic> order) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          langManager.getText('تم قبول الطلب بنجاح', 'Order Accepted Successfully'),
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.success,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              langManager.getText('تفاصيل الطلب:', 'Order Details:'),
              style: TextStyle(fontWeight: FontWeight.bold)
            ),
            SizedBox(height: 12),
            _buildOrderDetailRow(
              langManager.getText('من:', 'From:'),
              langManager.getText(order['from'], order['fromEn'])
            ),
            _buildOrderDetailRow(
              langManager.getText('إلى:', 'To:'),
              langManager.getText(order['to'], order['toEn'])
            ),
            _buildOrderDetailRow(
              langManager.getText('المسافة:', 'Distance:'),
              order['distance'] ?? '0 km'
            ),
            _buildOrderDetailRow(
              langManager.getText('المبلغ:', 'Amount:'),
              order['price'] is double ? '\$${order['price']}' : (order['price']?.toString() ?? '\$0')
            ),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Bootstrap.check_circle, color: AppColors.success, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      langManager.getText('تم قبول الطلب بنجاح', 'Order accepted successfully'),
                      style: TextStyle(
                        color: AppColors.success,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToMap(order); // الانتقال للخريطة
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: Text(langManager.getText('بدء الرحلة', 'Start Trip')),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetailRow(String label, dynamic value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value.toString(),
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToMap(Map<String, dynamic> order) {
    // الانتقال إلى شاشة الخريطة مع تمرير بيانات الطلب
    Navigator.pushNamed(
      context,
      '/map',
      arguments: {
        'orderId': order['id'],
        'from': order['from'],
        'fromEn': order['fromEn'],
        'to': order['to'],
        'toEn': order['toEn'],
        'distance': order['distance'],
        'customerName': order['customerName'],
        'customerNameEn': order['customerNameEn'],
        'price': order['price'],
      },
    );
  }

  void _declineOrder(String orderId) {
    setState(() {
      // إزالة الطلب من القائمة المتاحة
      _availableOrders.removeWhere((order) => order['id'] == orderId);
    });

    // عرض رسالة تأكيد
    _showInfoMessage('تم رفض الطلب', 'Order declined');
  }

  void _showSuccessMessage(String messageAr, String messageEn) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          langManager.getText(messageAr, messageEn),
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showInfoMessage(String messageAr, String messageEn) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          langManager.getText(messageAr, messageEn),
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.info,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  Widget _buildEmptyState(String messageAr, String messageEn, IconData icon) {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(40),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Icon(
                  icon,
                  color: Colors.white.withOpacity(0.6),
                  size: 40,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                langManager.getText(messageAr, messageEn),
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActiveOrderCard(Map<String, dynamic> order) {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.warning.withOpacity(0.2),
                AppColors.warning.withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: AppColors.warning.withOpacity(0.3),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            children: [
              // حالة الطلب
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.warning.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppColors.warning.withOpacity(0.5),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: AppColors.warning,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          langManager.getText(order['status'], order['statusEn']),
                          style: const TextStyle(
                            color: AppColors.warning,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Text(
                    langManager.getText('بدأت: ${order['startTime']}', 'Started: ${order['startTime']}'),
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // معلومات العميل والمسار
              _buildRouteInfo(order, langManager),
              const SizedBox(height: 16),
              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: _buildActionButton(
                      title: langManager.getText('اتصال', 'Call'),
                      color: AppColors.info,
                      onTap: () => _callCustomer(order['id']),
                      isOutlined: true,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildActionButton(
                      title: langManager.getText('إنهاء الرحلة', 'Complete Trip'),
                      color: AppColors.success,
                      onTap: () => _completeTrip(order['id']),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCompletedOrderCard(Map<String, dynamic> order) {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.success.withOpacity(0.15),
                AppColors.success.withOpacity(0.08),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: AppColors.success.withOpacity(0.3),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            children: [
              // معلومات الإكمال
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.success.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppColors.success.withOpacity(0.5),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Bootstrap.check_circle_fill,
                          color: AppColors.success,
                          size: 14,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          langManager.getText('مكتملة', 'Completed'),
                          style: const TextStyle(
                            color: AppColors.success,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Text(
                    order['completedTime'],
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // معلومات المسار
              _buildRouteInfo(order, langManager),
              const SizedBox(height: 16),
              // معلومات الأرباح والتقييم
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.success.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppColors.success.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            langManager.getText('الأرباح', 'Earnings'),
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.8),
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '\$${order['earnings'].toStringAsFixed(2)}',
                            style: const TextStyle(
                              color: AppColors.success,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.warning.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppColors.warning.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            langManager.getText('التقييم', 'Rating'),
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.8),
                              fontSize: 12,
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Bootstrap.star_fill,
                                color: AppColors.warning,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                order['rating'].toString(),
                                style: const TextStyle(
                                  color: AppColors.warning,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  void _callCustomer(String orderId) {
    print('Calling customer for order: $orderId');
    _showInfoMessage('جاري الاتصال بالعميل...', 'Calling customer...');
  }

  void _completeTrip(String orderId) {
    setState(() {
      // البحث عن الطلب في القائمة النشطة
      final orderIndex = _activeOrders.indexWhere((order) => order['id'] == orderId);
      if (orderIndex != -1) {
        final order = _activeOrders[orderIndex];

        // إضافة الطلب للطلبات المكتملة
        _completedOrders.insert(0, {
          ...order,
          'completedTime': '${DateTime.now().hour}:${DateTime.now().minute.toString().padLeft(2, '0')}',
          'earnings': order['price'],
          'rating': 4.5 + (math.Random().nextDouble() * 0.5), // تقييم عشوائي بين 4.5-5.0
        });

        // إزالة الطلب من القائمة النشطة
        _activeOrders.removeAt(orderIndex);

        // إعادة تعيين حالة الطلب النشط
        _hasActiveOrder = false;
        _currentActiveOrderId = null;
      }
    });

    _showSuccessMessage('تم إنهاء الرحلة بنجاح', 'Trip completed successfully');
  }

  // دالة لإكمال الطلب النشط الحالي (للاستدعاء من شاشة الخريطة)
  void completeCurrentActiveTrip() {
    if (_hasActiveOrder && _currentActiveOrderId != null) {
      _completeTrip(_currentActiveOrderId!);

      // التبديل إلى تبويب الطلبات المكتملة
      setState(() {
        _selectedTabIndex = 1;
      });
    }
  }
}

// فئة الرسام المخصص للخلفية المتحركة
class OrdersBackgroundPainter extends CustomPainter {
  final double animationValue;

  OrdersBackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // رسم دوائر متحركة
    _drawMovingCircles(canvas, size, paint);

    // رسم خطوط متموجة
    _drawWavyLines(canvas, size, paint);

    // رسم نقاط متلألئة
    _drawTwinklingDots(canvas, size, paint);
  }

  void _drawMovingCircles(Canvas canvas, Size size, Paint paint) {
    // دائرة كبيرة متحركة
    final circle1X = size.width * 0.2 + (size.width * 0.6 * animationValue);
    final circle1Y = size.height * 0.3;
    paint.color = Colors.white.withOpacity(0.02);
    canvas.drawCircle(Offset(circle1X, circle1Y), 80, paint);

    // دائرة متوسطة متحركة
    final circle2X = size.width * 0.8 - (size.width * 0.4 * animationValue);
    final circle2Y = size.height * 0.7;
    paint.color = Colors.white.withOpacity(0.03);
    canvas.drawCircle(Offset(circle2X, circle2Y), 60, paint);
  }

  void _drawWavyLines(Canvas canvas, Size size, Paint paint) {
    paint
      ..color = Colors.white.withOpacity(0.02)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final path = Path();
    final waveHeight = 30.0;
    final waveLength = size.width / 4;

    path.moveTo(0, size.height * 0.2);
    for (double x = 0; x <= size.width; x += 8) {
      final y = size.height * 0.2 +
                waveHeight * math.sin((x / waveLength + animationValue * 2) * 2 * math.pi);
      path.lineTo(x, y);
    }
    canvas.drawPath(path, paint);
  }

  void _drawTwinklingDots(Canvas canvas, Size size, Paint paint) {
    paint.style = PaintingStyle.fill;

    for (int i = 0; i < 20; i++) {
      final x = size.width * (i % 5) / 5 + (size.width * 0.1);
      final y = size.height * (i ~/ 5) / 4 + (size.height * 0.1);

      final opacity = (math.sin(animationValue * 4 * math.pi + i) + 1) / 2;
      paint.color = Colors.white.withOpacity(opacity * 0.06);

      canvas.drawCircle(Offset(x, y), 1.5, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}