# 🗺️ دليل إعداد Google Maps للتطبيق

## 📋 **الوضع الحالي:**

### ❌ **ما لا يعمل:**
- **الخريطة المعروضة في التطبيق وهمية** (مجرد تصميم)
- **لا يوجد Google Maps API Key**
- **لا يوجد تتبع موقع حقيقي**

### ✅ **ما يعمل:**
- **فتح تطبيقات الخرائط الخارجية** (Google Maps, Apple Maps)
- **المكتبات مثبتة** (google_maps_flutter, geolocator, location)
- **شاشة خريطة حقيقية جاهزة** (`RealMapScreen`)

---

## 🔧 **خطوات تفعيل الخرائط الحقيقية:**

### **1. الحصول على Google Maps API Key:**

#### **أ) إنشاء مشروع في Google Cloud:**
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل الـ APIs التالية:
   - **Maps SDK for Android**
   - **Maps SDK for iOS** 
   - **Geocoding API**
   - **Directions API**

#### **ب) إنشاء API Key:**
1. اذهب إلى **APIs & Services > Credentials**
2. اضغط **Create Credentials > API Key**
3. انسخ الـ API Key

#### **ج) تقييد الـ API Key (مهم للأمان):**
1. اضغط على الـ API Key المنشأ
2. في **Application restrictions**:
   - اختر **Android apps**
   - أضف package name: `com.hamoolati.driver.hamoolati_driver`
   - أضف SHA-1 fingerprint (احصل عليه من Android Studio)

---

### **2. إضافة API Key للتطبيق:**

#### **أ) Android (تم بالفعل):**
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<meta-data
    android:name="com.google.android.geo.API_KEY"
    android:value="YOUR_GOOGLE_MAPS_API_KEY_HERE"/>
```

#### **ب) iOS:**
```swift
// ios/Runner/AppDelegate.swift
import UIKit
import Flutter
import GoogleMaps

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GMSServices.provideAPIKey("YOUR_GOOGLE_MAPS_API_KEY_HERE")
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
```

---

### **3. إضافة صلاحيات الموقع:**

#### **أ) Android (موجود بالفعل):**
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
```

#### **ب) iOS:**
```xml
<!-- ios/Runner/Info.plist -->
<key>NSLocationWhenInUseUsageDescription</key>
<string>يحتاج التطبيق للوصول للموقع لعرض الخرائط والملاحة</string>
<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>يحتاج التطبيق للوصول للموقع لتتبع الرحلات</string>
```

---

## 🎯 **الميزات الجاهزة بعد الإعداد:**

### **1. شاشة الخريطة الحقيقية:**
- **موقع حقيقي مباشر** من GPS
- **تحديث الموقع كل 10 ثواني**
- **إضافة markers بالنقر**
- **تكبير وتصغير**
- **الانتقال للموقع الحالي**

### **2. الوصول للشاشة:**
```dart
// من أي مكان في التطبيق:
Navigator.pushNamed(context, '/real-map');

// أو من شاشة الخرائط الحالية:
// اضغط على زر "خريطة حقيقية" الأخضر
```

---

## 🔄 **خطوات التفعيل السريع:**

### **1. استبدال API Key:**
```bash
# في android/app/src/main/AndroidManifest.xml
# استبدل YOUR_GOOGLE_MAPS_API_KEY_HERE بالـ API Key الحقيقي
```

### **2. تشغيل التطبيق:**
```bash
flutter clean
flutter pub get
flutter run
```

### **3. اختبار الخريطة:**
1. افتح التطبيق
2. اذهب لشاشة الخرائط
3. اضغط على **"خريطة حقيقية"** (الزر الأخضر)
4. امنح صلاحية الموقع
5. ستظهر خريطة Google Maps حقيقية!

---

## 🚨 **ملاحظات مهمة:**

### **الأمان:**
- **لا تشارك API Key علناً**
- **قيّد الـ API Key للتطبيق فقط**
- **راقب الاستخدام في Google Cloud Console**

### **التكلفة:**
- **Google Maps مجاني** حتى حد معين شهرياً
- **راقب الاستخدام** لتجنب رسوم إضافية
- **فعّل تنبيهات الفوترة**

### **الاختبار:**
- **اختبر على جهاز حقيقي** (ليس محاكي)
- **تأكد من تفعيل GPS**
- **اختبر في مواقع مختلفة**

---

## 📱 **الميزات المتقدمة (اختيارية):**

### **1. تتبع المسار:**
```dart
// يمكن إضافة Polylines لرسم المسار
Set<Polyline> polylines = {
  Polyline(
    polylineId: PolylineId('route'),
    points: routePoints,
    color: AppColors.primary,
    width: 5,
  ),
};
```

### **2. Markers مخصصة:**
```dart
// يمكن إضافة أيقونات مخصصة للسائقين
BitmapDescriptor customIcon = await BitmapDescriptor.fromAssetImage(
  ImageConfiguration(size: Size(48, 48)),
  'assets/images/driver_icon.png',
);
```

### **3. تحديث الموقع المباشر:**
```dart
// يمكن ربط الموقع بقاعدة البيانات
await _realDataService.updateDriverLocation(
  driverId: currentUser.id,
  latitude: position.latitude,
  longitude: position.longitude,
);
```

---

## ✅ **الخلاصة:**

**بعد إضافة Google Maps API Key:**
- ✅ **خرائط حقيقية** بدلاً من الوهمية
- ✅ **موقع مباشر** من GPS
- ✅ **تفاعل كامل** مع الخريطة
- ✅ **تحديث مستمر** للموقع

**التطبيق سيصبح جاهز للاستخدام الحقيقي!** 🚀
