import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:icons_plus/icons_plus.dart';

import '../../core/constants/app_colors.dart';
import '../../core/utils/language_manager.dart';
import '../../shared/services/real_data_service.dart';
import '../../shared/models/order.dart';

class CreateOrderScreen extends StatefulWidget {
  const CreateOrderScreen({super.key});

  @override
  State<CreateOrderScreen> createState() => _CreateOrderScreenState();
}

class _CreateOrderScreenState extends State<CreateOrderScreen>
    with TickerProviderStateMixin {
  
  // Controllers للنماذج
  final _formKey = GlobalKey<FormState>();
  final _fromController = TextEditingController();
  final _toController = TextEditingController();
  final _cargoTypeController = TextEditingController();
  final _weightController = TextEditingController();
  final _notesController = TextEditingController();
  final _priceController = TextEditingController();
  
  // متغيرات الحالة
  bool _isLoading = false;
  String _selectedCargoType = 'عام';
  String _selectedPriority = 'عادي';
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  
  // Animation Controllers
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }
  
  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _fadeController.forward();
  }
  
  @override
  void dispose() {
    _fadeController.dispose();
    _fromController.dispose();
    _toController.dispose();
    _cargoTypeController.dispose();
    _weightController.dispose();
    _notesController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              langManager.getText('إنشاء طلب جديد', 'Create New Order'),
              style: GoogleFonts.tajawal(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            backgroundColor: AppColors.primary,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.white),
          ),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات الموقع
                    _buildLocationSection(langManager),
                    const SizedBox(height: 24),
                    
                    // معلومات الحمولة
                    _buildCargoSection(langManager),
                    const SizedBox(height: 24),
                    
                    // معلومات إضافية
                    _buildAdditionalInfoSection(langManager),
                    const SizedBox(height: 32),
                    
                    // زر الإنشاء
                    _buildCreateButton(langManager),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildLocationSection(LanguageManager langManager) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Bootstrap.geo_alt,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                langManager.getText('معلومات الموقع', 'Location Information'),
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // من
          TextFormField(
            controller: _fromController,
            decoration: InputDecoration(
              labelText: langManager.getText('من', 'From'),
              hintText: langManager.getText('أدخل موقع الاستلام', 'Enter pickup location'),
              prefixIcon: const Icon(Bootstrap.geo_alt_fill),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return langManager.getText('يرجى إدخال موقع الاستلام', 'Please enter pickup location');
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // إلى
          TextFormField(
            controller: _toController,
            decoration: InputDecoration(
              labelText: langManager.getText('إلى', 'To'),
              hintText: langManager.getText('أدخل موقع التسليم', 'Enter delivery location'),
              prefixIcon: const Icon(Bootstrap.geo_alt),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return langManager.getText('يرجى إدخال موقع التسليم', 'Please enter delivery location');
              }
              return null;
            },
          ),
        ],
      ),
    );
  }
  
  Widget _buildCargoSection(LanguageManager langManager) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Bootstrap.box,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                langManager.getText('معلومات الحمولة', 'Cargo Information'),
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // نوع الحمولة
          DropdownButtonFormField<String>(
            value: _selectedCargoType,
            decoration: InputDecoration(
              labelText: langManager.getText('نوع الحمولة', 'Cargo Type'),
              prefixIcon: const Icon(Bootstrap.box_seam),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            items: [
              'عام',
              'أثاث',
              'إلكترونيات',
              'مواد غذائية',
              'مواد بناء',
              'أخرى'
            ].map((type) => DropdownMenuItem(
              value: type,
              child: Text(type),
            )).toList(),
            onChanged: (value) {
              setState(() {
                _selectedCargoType = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          
          // الوزن
          TextFormField(
            controller: _weightController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: langManager.getText('الوزن (كيلو)', 'Weight (kg)'),
              hintText: langManager.getText('أدخل الوزن التقريبي', 'Enter approximate weight'),
              prefixIcon: const Icon(Bootstrap.speedometer),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildAdditionalInfoSection(LanguageManager langManager) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Bootstrap.info_circle,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                langManager.getText('معلومات إضافية', 'Additional Information'),
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // السعر المقترح
          TextFormField(
            controller: _priceController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: langManager.getText('السعر المقترح (دينار)', 'Suggested Price (IQD)'),
              hintText: langManager.getText('أدخل السعر المقترح', 'Enter suggested price'),
              prefixIcon: const Icon(Bootstrap.currency_dollar),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // ملاحظات
          TextFormField(
            controller: _notesController,
            maxLines: 3,
            decoration: InputDecoration(
              labelText: langManager.getText('ملاحظات', 'Notes'),
              hintText: langManager.getText('أي ملاحظات إضافية...', 'Any additional notes...'),
              prefixIcon: const Icon(Bootstrap.chat_text),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildCreateButton(LanguageManager langManager) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : () => _createOrder(langManager),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 2,
        ),
        child: _isLoading
            ? const CircularProgressIndicator(color: Colors.white)
            : Text(
                langManager.getText('إنشاء الطلب', 'Create Order'),
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }
  
  Future<void> _createOrder(LanguageManager langManager) async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final dataService = Provider.of<RealDataService>(context, listen: false);
      final currentUser = dataService.currentUser;
      
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل دخول');
      }
      
      // إنشاء الطلب
      final order = Order(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        companyId: currentUser.id,
        companyName: currentUser.name,
        from: _fromController.text,
        to: _toController.text,
        cargoType: _selectedCargoType,
        weight: double.tryParse(_weightController.text) ?? 0,
        price: double.tryParse(_priceController.text) ?? 0,
        notes: _notesController.text,
        status: OrderStatus.pending,
        createdAt: DateTime.now(),
      );
      
      await dataService.createOrder(order);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              langManager.getText('تم إنشاء الطلب بنجاح', 'Order created successfully'),
              style: GoogleFonts.tajawal(),
            ),
            backgroundColor: AppColors.success,
          ),
        );
        
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              langManager.getText('خطأ في إنشاء الطلب: $e', 'Error creating order: $e'),
              style: GoogleFonts.tajawal(),
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
