import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;

import '../../core/constants/app_colors.dart';
import '../../core/utils/language_manager.dart';

class CompanyRegisterScreen extends StatefulWidget {
  const CompanyRegisterScreen({super.key});

  @override
  State<CompanyRegisterScreen> createState() => _CompanyRegisterScreenState();
}

class _CompanyRegisterScreenState extends State<CompanyRegisterScreen>
    with TickerProviderStateMixin {

  // ═══════════════════════════════════════════════════════════════
  // متحكمات النصوص والنماذج (Text Controllers & Form)
  // ═══════════════════════════════════════════════════════════════

  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();

  // معلومات الشركة الأساسية
  final _companyNameController = TextEditingController();
  final _businessTypeController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  // معلومات قانونية
  final _commercialRegisterController = TextEditingController();
  final _vatNumberController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();

  // متغير للمحافظة المختارة
  String? _selectedProvince;

  // تفاصيل الاتصال
  final _contactPersonController = TextEditingController();
  final _positionController = TextEditingController();
  final _directPhoneController = TextEditingController();

  // إعدادات الحساب
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // ═══════════════════════════════════════════════════════════════
  // متغيرات الحالة (State Variables)
  // ═══════════════════════════════════════════════════════════════

  int _currentStep = 0;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _agreeToTerms = false;
  bool _isLoading = false;

  String? _selectedBusinessType;
  String? _selectedCity;

  // ═══════════════════════════════════════════════════════════════
  // متحكمات الأنيميشن (Animation Controllers)
  // ═══════════════════════════════════════════════════════════════

  late AnimationController _fadeController;
  late AnimationController _backgroundController;
  late AnimationController _stepController;

  late Animation<double> _fadeAnimation;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _stepAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _detectUserLocation();
  }

  // قائمة المحافظات العراقية
  final List<String> _provinces = [
    'بغداد',
    'البصرة',
    'نينوى',
    'أربيل',
    'النجف',
    'كربلاء',
    'الأنبار',
    'ديالى',
    'ذي قار',
    'المثنى',
    'القادسية',
    'بابل',
    'كركوك',
    'صلاح الدين',
    'واسط',
    'ميسان',
    'دهوك',
    'السليمانية',
  ];

  // دالة لاكتشاف موقع المستخدم واختيار المحافظة تلقائياً
  void _detectUserLocation() async {
    // يمكن هنا إضافة كود لاكتشاف الموقع الجغرافي
    // وتحديد المحافظة بناءً على الإحداثيات
    // للآن سنضع بغداد كافتراضي
    setState(() {
      _selectedProvince = 'بغداد';
    });
  }

  void _initializeAnimations() {
    // أنيميشن الظهور التدريجي
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // أنيميشن الخلفية المتحركة
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    );

    // أنيميشن الخطوات
    _stepController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.linear,
    ));

    _stepAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _stepController,
      curve: Curves.elasticOut,
    ));
  }

  void _startAnimations() {
    _fadeController.forward();
    // إيقاف أنيميشن الخلفية المتكرر
    // _backgroundController.repeat();
    _stepController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _backgroundController.dispose();
    _stepController.dispose();
    _pageController.dispose();

    // تنظيف متحكمات النصوص
    _companyNameController.dispose();
    _businessTypeController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _commercialRegisterController.dispose();
    _vatNumberController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _contactPersonController.dispose();
    _positionController.dispose();
    _directPhoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // الخلفية الثابتة
          _buildStaticBackground(),

              // المحتوى الرئيسي
              SafeArea(
                child: Column(
                  children: [
                    // الهيدر مع مؤشر التقدم
                    _buildHeader(),

                    // محتوى الخطوات
                    Expanded(
                      child: PageView(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _currentStep = index;
                          });
                          _stepController.reset();
                          _stepController.forward();
                        },
                        children: [
                          _buildBasicInfoStep(),
                          _buildLegalInfoStep(),
                          _buildContactDetailsStep(),
                          _buildDocumentsStep(),
                          _buildAccountSettingsStep(),
                        ],
                      ),
                    ),

                    // أزرار التنقل
                    _buildNavigationButtons(),
                  ],
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildStaticBackground() {
    return Positioned.fill(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF1E3A8A), // أزرق داكن
              Color(0xFF3B82F6), // أزرق متوسط
              Color(0xFF1E40AF), // أزرق عميق
              Color(0xFF2563EB), // أزرق فاتح
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // زر الرجوع والعنوان
                Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        langManager.isArabic ? Icons.arrow_forward_ios : Icons.arrow_back_ios,
                        color: Colors.white,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        langManager.getText('إنشاء حساب شركة', 'Create Company Account'),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 48), // لتوسيط العنوان
                  ],
                ),

                const SizedBox(height: 20),

                // مؤشر التقدم
                _buildProgressIndicator(langManager),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildProgressIndicator(LanguageManager langManager) {
    final steps = [
      langManager.getText('المعلومات الأساسية', 'Basic Info'),
      langManager.getText('المعلومات القانونية', 'Legal Info'),
      langManager.getText('تفاصيل الاتصال', 'Contact Details'),
      langManager.getText('الوثائق', 'Documents'),
      langManager.getText('إعدادات الحساب', 'Account Settings'),
    ];

    return Column(
      children: [
        // شريط التقدم
        Container(
          height: 4,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.3),
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: (_currentStep + 1) / steps.length,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),

        const SizedBox(height: 12),

        // عنوان الخطوة الحالية
        Text(
          '${langManager.getText('الخطوة', 'Step')} ${_currentStep + 1} ${langManager.getText('من', 'of')} ${steps.length}',
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),

        const SizedBox(height: 4),

        Text(
          steps[_currentStep],
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildBasicInfoStep() {
    return ScaleTransition(
      scale: _stepAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // أيقونة الخطوة
                    Center(
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 2,
                          ),
                        ),
                        child: const Icon(
                          Bootstrap.building,
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
                    ),

                    const SizedBox(height: 30),

                    // حقل اسم الشركة
                    _buildTextField(
                      controller: _companyNameController,
                      label: langManager.getText('اسم الشركة', 'Company Name'),
                      hint: langManager.getText('أدخل اسم الشركة الرسمي', 'Enter official company name'),
                      icon: Bootstrap.building,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return langManager.getText('يرجى إدخال اسم الشركة', 'Please enter company name');
                        }

                        // التحقق من طول الاسم
                        if (value.trim().length < 2) {
                          return langManager.getText('اسم الشركة قصير جداً', 'Company name is too short');
                        }

                        if (value.trim().length > 100) {
                          return langManager.getText('اسم الشركة طويل جداً', 'Company name is too long');
                        }

                        // التحقق من الأحرف المسموحة (عربي، إنجليزي، أرقام، مسافات، رموز أساسية)
                        if (!RegExp(r'^[\u0600-\u06FFa-zA-Z0-9\s\-\.\&\(\)]+$').hasMatch(value.trim())) {
                          return langManager.getText(
                            'اسم الشركة يحتوي على رموز غير مسموحة',
                            'Company name contains invalid characters'
                          );
                        }

                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // نوع النشاط التجاري
                    _buildDropdownField(
                      label: langManager.getText('نوع النشاط التجاري', 'Business Type'),
                      hint: langManager.getText('اختر نوع النشاط', 'Select business type'),
                      icon: Bootstrap.briefcase,
                      value: _selectedBusinessType,
                      items: [
                        langManager.getText('شركة نقل', 'Transport Company'),
                        langManager.getText('مصنع', 'Factory'),
                        langManager.getText('معمل', 'Plant'),
                        langManager.getText('مستودع', 'Warehouse'),
                        langManager.getText('شركة تجارية', 'Trading Company'),
                        langManager.getText('أخرى', 'Other'),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedBusinessType = value;
                        });
                      },
                    ),

                    const SizedBox(height: 20),

                    // البريد الإلكتروني
                    _buildTextField(
                      controller: _emailController,
                      label: langManager.getText('البريد الإلكتروني', 'Email Address'),
                      hint: langManager.getText('أدخل البريد الإلكتروني للشركة', 'Enter company email'),
                      icon: Bootstrap.envelope,
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return langManager.getText('يرجى إدخال البريد الإلكتروني', 'Please enter email address');
                        }
                        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                          return langManager.getText('البريد الإلكتروني غير صحيح', 'Invalid email address');
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // رقم الهاتف الرئيسي
                    _buildTextField(
                      controller: _phoneController,
                      label: langManager.getText('رقم الهاتف الرئيسي', 'Main Phone Number'),
                      hint: langManager.getText('07XXXXXXXXX أو +9647XXXXXXXXX', '07XXXXXXXXX or +9647XXXXXXXXX'),
                      icon: Bootstrap.telephone,
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return langManager.getText('يرجى إدخال رقم الهاتف', 'Please enter phone number');
                        }

                        // إزالة المسافات والرموز الإضافية
                        String cleanedValue = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');

                        // التحقق من الأنماط المختلفة لأرقام الهواتف العراقية
                        bool isValid = false;

                        // نمط 07XXXXXXXXX (11 رقم - الصيغة الصحيحة)
                        if (RegExp(r'^07[0-9]{9}$').hasMatch(cleanedValue)) {
                          isValid = true;
                        }
                        // نمط +9647XXXXXXXXX (14 رقم مع رمز الدولة)
                        else if (RegExp(r'^\+9647[0-9]{9}$').hasMatch(cleanedValue)) {
                          isValid = true;
                        }
                        // نمط 009647XXXXXXXXX (15 رقم مع رمز الدولة)
                        else if (RegExp(r'^009647[0-9]{9}$').hasMatch(cleanedValue)) {
                          isValid = true;
                        }
                        // نمط 9647XXXXXXXXX (13 رقم بدون +)
                        else if (RegExp(r'^9647[0-9]{9}$').hasMatch(cleanedValue)) {
                          isValid = true;
                        }
                        // دعم الصيغة القديمة 07XXXXXXXX (10 أرقام) للتوافق
                        else if (RegExp(r'^07[0-9]{8}$').hasMatch(cleanedValue)) {
                          isValid = true;
                        }

                        if (!isValid) {
                          return langManager.getText(
                            'رقم الهاتف غير صحيح. استخدم: 07XXXXXXXXX أو +9647XXXXXXXXX',
                            'Invalid phone number. Use: 07XXXXXXXXX or +9647XXXXXXXXX'
                          );
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLegalInfoStep() {
    return ScaleTransition(
      scale: _stepAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // أيقونة الخطوة
                  Center(
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 2,
                        ),
                      ),
                      child: const Icon(
                        Bootstrap.file_earmark_text,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ),

                  const SizedBox(height: 30),

                  // العنوان الرئيسي
                  _buildTextField(
                    controller: _addressController,
                    label: langManager.getText('العنوان الرئيسي للشركة', 'Main Company Address'),
                    hint: langManager.getText('أدخل العنوان التفصيلي', 'Enter detailed address'),
                    icon: Bootstrap.geo_alt,
                    maxLines: 3,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return langManager.getText('يرجى إدخال العنوان', 'Please enter address');
                      }

                      // التحقق من طول العنوان
                      if (value.trim().length < 5) {
                        return langManager.getText('العنوان قصير جداً', 'Address is too short');
                      }

                      if (value.trim().length > 200) {
                        return langManager.getText('العنوان طويل جداً', 'Address is too long');
                      }

                      // التحقق من الأحرف المسموحة (عربي، إنجليزي، أرقام، رموز العناوين)
                      if (!RegExp(r'^[\u0600-\u06FFa-zA-Z0-9\s\-\.\,\/\#\(\)]+$').hasMatch(value.trim())) {
                        return langManager.getText(
                          'العنوان يحتوي على رموز غير مسموحة',
                          'Address contains invalid characters'
                        );
                      }

                      return null;
                    },
                  ),

                  const SizedBox(height: 20),

                  // المحافظة (اختيار تلقائي)
                  _buildDropdownField(
                    label: langManager.getText('المحافظة', 'Province'),
                    hint: langManager.getText('اختر المحافظة', 'Select province'),
                    icon: Bootstrap.geo_alt,
                    value: _selectedProvince,
                    items: _provinces,
                    onChanged: (value) {
                      setState(() {
                        _selectedProvince = value;
                      });
                    },
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildContactDetailsStep() {
    return ScaleTransition(
      scale: _stepAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // أيقونة الخطوة
                  Center(
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 2,
                        ),
                      ),
                      child: const Icon(
                        Bootstrap.person_badge,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ),

                  const SizedBox(height: 30),

                  // اسم الشخص المسؤول
                  _buildTextField(
                    controller: _contactPersonController,
                    label: langManager.getText('اسم الشخص المسؤول', 'Contact Person Name'),
                    hint: langManager.getText('أدخل اسم الشخص المسؤول', 'Enter contact person name'),
                    icon: Bootstrap.person,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return langManager.getText('يرجى إدخال اسم الشخص المسؤول', 'Please enter contact person name');
                      }

                      // التحقق من طول الاسم
                      if (value.trim().length < 2) {
                        return langManager.getText('الاسم قصير جداً', 'Name is too short');
                      }

                      if (value.trim().length > 50) {
                        return langManager.getText('الاسم طويل جداً', 'Name is too long');
                      }

                      // التحقق من الأحرف المسموحة (عربي، إنجليزي، مسافات)
                      if (!RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$').hasMatch(value.trim())) {
                        return langManager.getText(
                          'الاسم يحتوي على رموز غير مسموحة',
                          'Name contains invalid characters'
                        );
                      }

                      return null;
                    },
                  ),

                  const SizedBox(height: 20),

                  // منصبه في الشركة
                  _buildTextField(
                    controller: _positionController,
                    label: langManager.getText('منصبه في الشركة', 'Position in Company'),
                    hint: langManager.getText('مثل: مدير عام، مدير العمليات', 'e.g: General Manager, Operations Manager'),
                    icon: Bootstrap.briefcase,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return langManager.getText('يرجى إدخال المنصب', 'Please enter position');
                      }

                      // التحقق من طول المنصب
                      if (value.trim().length < 2) {
                        return langManager.getText('المنصب قصير جداً', 'Position is too short');
                      }

                      if (value.trim().length > 50) {
                        return langManager.getText('المنصب طويل جداً', 'Position is too long');
                      }

                      // التحقق من الأحرف المسموحة (عربي، إنجليزي، مسافات، فواصل)
                      if (!RegExp(r'^[\u0600-\u06FFa-zA-Z\s\,\-]+$').hasMatch(value.trim())) {
                        return langManager.getText(
                          'المنصب يحتوي على رموز غير مسموحة',
                          'Position contains invalid characters'
                        );
                      }

                      return null;
                    },
                  ),

                  const SizedBox(height: 20),

                  // رقم هاتف مباشر
                  _buildTextField(
                    controller: _directPhoneController,
                    label: langManager.getText('رقم هاتف مباشر', 'Direct Phone Number'),
                    hint: langManager.getText('07XXXXXXXXX أو +9647XXXXXXXXX', '07XXXXXXXXX or +9647XXXXXXXXX'),
                    icon: Bootstrap.telephone,
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return langManager.getText('يرجى إدخال رقم الهاتف المباشر', 'Please enter direct phone number');
                      }

                      // إزالة المسافات والرموز الإضافية
                      String cleanedValue = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');

                      // التحقق من الأنماط المختلفة لأرقام الهواتف العراقية
                      bool isValid = false;

                      // نمط 07XXXXXXXXX (11 رقم - الصيغة الصحيحة)
                      if (RegExp(r'^07[0-9]{9}$').hasMatch(cleanedValue)) {
                        isValid = true;
                      }
                      // نمط +9647XXXXXXXXX (14 رقم مع رمز الدولة)
                      else if (RegExp(r'^\+9647[0-9]{9}$').hasMatch(cleanedValue)) {
                        isValid = true;
                      }
                      // نمط 009647XXXXXXXXX (15 رقم مع رمز الدولة)
                      else if (RegExp(r'^009647[0-9]{9}$').hasMatch(cleanedValue)) {
                        isValid = true;
                      }
                      // نمط 9647XXXXXXXXX (13 رقم بدون +)
                      else if (RegExp(r'^9647[0-9]{9}$').hasMatch(cleanedValue)) {
                        isValid = true;
                      }
                      // دعم الصيغة القديمة 07XXXXXXXX (10 أرقام) للتوافق
                      else if (RegExp(r'^07[0-9]{8}$').hasMatch(cleanedValue)) {
                        isValid = true;
                      }

                      if (!isValid) {
                        return langManager.getText(
                          'رقم الهاتف غير صحيح. استخدم: 07XXXXXXXXX أو +9647XXXXXXXXX',
                          'Invalid phone number. Use: 07XXXXXXXXX or +9647XXXXXXXXX'
                        );
                      }
                      return null;
                    },
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildDocumentsStep() {
    return ScaleTransition(
      scale: _stepAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // أيقونة الخطوة
                  Center(
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 2,
                        ),
                      ),
                      child: const Icon(
                        Bootstrap.cloud_upload,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ),

                  const SizedBox(height: 30),

                  Text(
                    langManager.getText('الوثائق المطلوبة', 'Required Documents'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 20),

                  // شعار الشركة
                  _buildDocumentUpload(
                    title: langManager.getText('شعار الشركة', 'Company Logo'),
                    subtitle: langManager.getText('اختياري - يفضل PNG أو JPG', 'Optional - PNG or JPG preferred'),
                    icon: Bootstrap.image,
                    onTap: () => _uploadDocument('logo'),
                  ),

                  const SizedBox(height: 16),

                  // السجل التجاري
                  _buildDocumentUpload(
                    title: langManager.getText('السجل التجاري', 'Commercial Register'),
                    subtitle: langManager.getText('مطلوب - صورة واضحة للسجل', 'Required - Clear image of register'),
                    icon: Bootstrap.file_earmark_text,
                    onTap: () => _uploadDocument('commercial_register'),
                    isRequired: true,
                  ),

                  const SizedBox(height: 16),

                  // وثيقة ضريبة القيمة المضافة
                  _buildDocumentUpload(
                    title: langManager.getText('وثيقة ضريبة القيمة المضافة', 'VAT Document'),
                    subtitle: langManager.getText('مطلوب - شهادة ضريبة القيمة المضافة', 'Required - VAT certificate'),
                    icon: Bootstrap.receipt,
                    onTap: () => _uploadDocument('vat_document'),
                    isRequired: true,
                  ),

                  const SizedBox(height: 16),


                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildAccountSettingsStep() {
    return ScaleTransition(
      scale: _stepAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // أيقونة الخطوة
                  Center(
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 2,
                        ),
                      ),
                      child: const Icon(
                        Bootstrap.shield_check,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ),

                  const SizedBox(height: 30),

                  // كلمة المرور
                  _buildTextField(
                    controller: _passwordController,
                    label: langManager.getText('كلمة المرور', 'Password'),
                    hint: langManager.getText('أدخل كلمة مرور قوية', 'Enter strong password'),
                    icon: Bootstrap.lock,
                    obscureText: !_isPasswordVisible,
                    suffixIcon: IconButton(
                      onPressed: () {
                        setState(() {
                          _isPasswordVisible = !_isPasswordVisible;
                        });
                      },
                      icon: Icon(
                        _isPasswordVisible ? Bootstrap.eye_slash : Bootstrap.eye,
                        color: Colors.white70,
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return langManager.getText('يرجى إدخال كلمة المرور', 'Please enter password');
                      }
                      if (value.length < 8) {
                        return langManager.getText('كلمة المرور يجب أن تكون 8 أحرف على الأقل', 'Password must be at least 8 characters');
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 20),

                  // تأكيد كلمة المرور
                  _buildTextField(
                    controller: _confirmPasswordController,
                    label: langManager.getText('تأكيد كلمة المرور', 'Confirm Password'),
                    hint: langManager.getText('أعد إدخال كلمة المرور', 'Re-enter password'),
                    icon: Bootstrap.lock,
                    obscureText: !_isConfirmPasswordVisible,
                    suffixIcon: IconButton(
                      onPressed: () {
                        setState(() {
                          _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                        });
                      },
                      icon: Icon(
                        _isConfirmPasswordVisible ? Bootstrap.eye_slash : Bootstrap.eye,
                        color: Colors.white70,
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return langManager.getText('يرجى تأكيد كلمة المرور', 'Please confirm password');
                      }
                      if (value != _passwordController.text) {
                        return langManager.getText('كلمة المرور غير متطابقة', 'Passwords do not match');
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 30),

                  // موافقة على الشروط والأحكام
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Checkbox(
                          value: _agreeToTerms,
                          onChanged: (value) {
                            setState(() {
                              _agreeToTerms = value ?? false;
                            });
                          },
                          activeColor: AppColors.primary,
                          checkColor: Colors.white,
                          side: BorderSide(
                            color: AppColors.primary.withOpacity(0.7),
                            width: 2,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _agreeToTerms = !_agreeToTerms;
                              });
                            },
                            child: Text(
                              langManager.getText(
                                'أوافق على الشروط والأحكام وسياسة الخصوصية',
                                'I agree to Terms and Conditions and Privacy Policy',
                              ),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: maxLines > 1 ? TextInputType.multiline : (keyboardType ?? TextInputType.text),
        obscureText: obscureText,
        maxLines: maxLines,
        validator: validator,
        textInputAction: maxLines > 1 ? TextInputAction.newline : TextInputAction.next,
        enableSuggestions: true,
        autocorrect: false,
        textCapitalization: TextCapitalization.none,
        style: GoogleFonts.tajawal(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
          height: 1.3,
        ),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: GoogleFonts.tajawal(
            color: Colors.white70,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
          hintStyle: GoogleFonts.tajawal(
            color: Colors.white54,
            fontSize: 14,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          suffixIcon: suffixIcon != null
              ? Theme(
                  data: Theme.of(context).copyWith(
                    iconTheme: const IconThemeData(color: Colors.white70),
                  ),
                  child: suffixIcon,
                )
              : null,
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          fillColor: Colors.transparent,
          filled: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          errorStyle: const TextStyle(
            color: Colors.redAccent,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String hint,
    required IconData icon,
    required String? value,
    required List<String> items,
    required void Function(String?) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DropdownButtonFormField<String>(
        value: value,
        onChanged: onChanged,
        dropdownColor: const Color(0xFF1A1A1A),
        style: GoogleFonts.tajawal(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: GoogleFonts.tajawal(
            color: Colors.white70,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
          hintStyle: GoogleFonts.tajawal(
            color: Colors.white54,
            fontSize: 14,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          fillColor: Colors.transparent,
          filled: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              style: GoogleFonts.tajawal(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        }).toList(),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return Provider.of<LanguageManager>(context, listen: false)
                .getText('يرجى الاختيار من القائمة', 'Please select from list');
          }
          return null;
        },
        icon: Icon(
          Icons.keyboard_arrow_down,
          color: Colors.white70,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildDocumentUpload({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool isRequired = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isRequired ? Colors.orange.withOpacity(0.5) : Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (isRequired) ...[
                        const SizedBox(width: 4),
                        const Text(
                          '*',
                          style: TextStyle(
                            color: Colors.orange,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Bootstrap.cloud_upload,
              color: Colors.white.withOpacity(0.7),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              // زر السابق
              if (_currentStep > 0)
                Expanded(
                  child: ElevatedButton(
                    onPressed: _previousStep,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white.withOpacity(0.2),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          langManager.isArabic ? Icons.arrow_forward_ios : Icons.arrow_back_ios,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          langManager.getText('السابق', 'Previous'),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              if (_currentStep > 0) const SizedBox(width: 16),

              // زر التالي أو إنشاء الحساب
              Expanded(
                flex: _currentStep == 0 ? 1 : 1,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : (_currentStep == 4 ? _createAccount : _nextStep),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: AppColors.primary,
                    elevation: 8,
                    shadowColor: Colors.black.withOpacity(0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: AppColors.primary,
                            strokeWidth: 2,
                          ),
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              _currentStep == 4
                                  ? langManager.getText('إنشاء الحساب', 'Create Account')
                                  : langManager.getText('التالي', 'Next'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              _currentStep == 4
                                  ? Bootstrap.check_circle
                                  : (langManager.isArabic ? Icons.arrow_back_ios : Icons.arrow_forward_ios),
                              size: 16,
                            ),
                          ],
                        ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال التعامل مع الأحداث (Event Handlers)
  // ═══════════════════════════════════════════════════════════════

  void _nextStep() {
    if (_currentStep < 4) {
      if (_validateCurrentStep()) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0: // المعلومات الأساسية
        if (!_formKey.currentState!.validate()) return false;
        if (_selectedBusinessType == null) {
          _showErrorMessage('يرجى اختيار نوع النشاط التجاري', 'Please select business type');
          return false;
        }
        break;
      case 1: // المعلومات القانونية
        if (_addressController.text.isEmpty ||
            _selectedProvince == null) {
          _showErrorMessage('يرجى إكمال جميع الحقول المطلوبة', 'Please complete all required fields');
          return false;
        }
        break;
      case 2: // تفاصيل الاتصال
        if (_contactPersonController.text.isEmpty ||
            _positionController.text.isEmpty ||
            _directPhoneController.text.isEmpty) {
          _showErrorMessage('يرجى إكمال جميع الحقول المطلوبة', 'Please complete all required fields');
          return false;
        }
        break;
      case 3: // الوثائق
        // يمكن إضافة التحقق من رفع الوثائق هنا
        break;
      case 4: // إعدادات الحساب
        if (_passwordController.text.isEmpty ||
            _confirmPasswordController.text.isEmpty ||
            !_agreeToTerms) {
          _showErrorMessage('يرجى إكمال جميع الحقول والموافقة على الشروط', 'Please complete all fields and agree to terms');
          return false;
        }
        break;
    }
    return true;
  }

  void _createAccount() async {
    if (!_validateCurrentStep()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // استدعاء تسجيل الشركة الحقيقي
      final success = await authProvider.registerCompany(
        companyName: _companyNameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
        password: _passwordController.text,
        businessLicense: 'TEMP_LICENSE_${DateTime.now().millisecondsSinceEpoch}', // رقم ترخيص مؤقت
        address: _addressController.text.trim(),
        website: null,
        description: _selectedBusinessType,
      );

      if (mounted) {
        if (success) {
          // عرض رسالة نجاح
          _showSuccessMessage(
            'تم إنشاء حساب الشركة بنجاح',
            'Company account created successfully',
          );

          // الانتقال لشاشة تسجيل الدخول
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              Navigator.pushReplacementNamed(context, '/company-login');
            }
          });
        } else {
          _showErrorMessage(
            authProvider.errorMessage ?? 'حدث خطأ أثناء إنشاء الحساب',
            authProvider.errorMessage ?? 'Error occurred while creating account'
          );
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage('حدث خطأ أثناء إنشاء الحساب', 'Error occurred while creating account');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _uploadDocument(String documentType) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            langManager.getText('رفع الوثيقة', 'Upload Document'),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(
            langManager.getText(
              'سيتم إضافة خاصية رفع الوثائق قريباً',
              'Document upload feature will be added soon',
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(langManager.getText('موافق', 'OK')),
            ),
          ],
        );
      },
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال عرض الرسائل (Message Functions)
  // ═══════════════════════════════════════════════════════════════

  void _showSuccessMessage(String messageAr, String messageEn) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          langManager.getText(messageAr, messageEn),
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showErrorMessage(String messageAr, String messageEn) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          langManager.getText(messageAr, messageEn),
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}

// ═══════════════════════════════════════════════════════════════
// فئة الرسام المخصص للخلفية المتحركة
// ═══════════════════════════════════════════════════════════════

class CompanyRegisterBackgroundPainter extends CustomPainter {
  final double animationValue;

  CompanyRegisterBackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.03)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    // رسم شبكة متحركة
    final gridSize = 50.0;
    final offsetX = (animationValue * gridSize * 0.5) % gridSize;
    final offsetY = (animationValue * gridSize * 0.3) % gridSize;

    // خطوط عمودية
    for (double x = -gridSize + offsetX; x < size.width + gridSize; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // خطوط أفقية
    for (double y = -gridSize + offsetY; y < size.height + gridSize; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // رسم أشكال هندسية متحركة
    final shapePaint = Paint()
      ..color = Colors.white.withOpacity(0.04)
      ..style = PaintingStyle.fill;

    final shapeCount = 6;
    for (int i = 0; i < shapeCount; i++) {
      final angle = (animationValue * 2 * math.pi * 0.5) + (i * 2 * math.pi / shapeCount);
      final radius = 40.0 + (math.sin(animationValue * 2 * math.pi + i) * 15);
      final centerX = size.width * 0.2 + math.cos(angle) * size.width * 0.6;
      final centerY = size.height * 0.3 + math.sin(angle) * size.height * 0.4;

      // رسم مربعات دوارة
      canvas.save();
      canvas.translate(centerX, centerY);
      canvas.rotate(animationValue * 2 * math.pi + i);
      canvas.drawRect(
        Rect.fromCenter(center: Offset.zero, width: radius, height: radius),
        shapePaint,
      );
      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(CompanyRegisterBackgroundPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}