import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:math' as math;

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_dimensions.dart';
import '../../core/utils/language_manager.dart';
import '../../core/services/driver_location_service.dart';
import '../../shared/widgets/company_bottom_navigation.dart';
import 'live_tracking_map_screen.dart';

class TrackingScreen extends StatefulWidget {
  const TrackingScreen({super.key});

  @override
  State<TrackingScreen> createState() => _TrackingScreenState();
}

class _TrackingScreenState extends State<TrackingScreen>
    with TickerProviderStateMixin {

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _backgroundController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _backgroundAnimation;



  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  final List<Map<String, dynamic>> _shipments = [
    {
      'id': 'TRK-001',
      'orderId': 'ORD-001',
      'cargoType': 'بضائع عامة',
      'from': 'بغداد - الكرادة',
      'to': 'البصرة - الجبيلة',
      'status': 'في الطريق',
      'progress': 0.65,
      'driver': 'محمد أحمد حسن',
      'vehicle': 'شاحنة جادر متحرك 13.60م',
      'estimatedTime': '2 ساعة',
      'currentLocation': 'الناصرية - وسط المدينة',
      'startTime': '08:30',
      'estimatedArrival': '16:45',
      'distance': '420 كم',
      'completedDistance': '273 كم',
      'customer': 'أحمد محمد علي',
      'phone': '07701234567',
      'driverLatitude': 31.0461,
      'driverLongitude': 46.2588,
      'currentSpeed': 85, // كم/ساعة
      'speedLimit': 90, // كم/ساعة
    },
    {
      'id': 'TRK-003',
      'orderId': 'ORD-003',
      'cargoType': 'أجهزة إلكترونية',
      'from': 'بغداد - المنصور',
      'to': 'النجف - الكوفة',
      'status': 'متأخر',
      'progress': 0.45,
      'driver': 'حسام محمد علي',
      'vehicle': 'أتيكو 6 متر',
      'estimatedTime': '4 ساعات',
      'currentLocation': 'الحلة - مركز المدينة',
      'startTime': '10:15',
      'estimatedArrival': '18:00',
      'distance': '180 كم',
      'completedDistance': '81 كم',
      'customer': 'سارة أحمد علي',
      'phone': '07901234567',
      'driverLatitude': 32.4637,
      'driverLongitude': 44.4206,
      'currentSpeed': 95, // كم/ساعة
      'speedLimit': 90, // كم/ساعة
    },
    {
      'id': 'TRK-004',
      'orderId': 'ORD-004',
      'cargoType': 'مواد بناء',
      'from': 'بغداد - الشعلة',
      'to': 'الموصل - الجامعة',
      'status': 'في الطريق',
      'progress': 0.30,
      'driver': 'نور الدين أحمد',
      'vehicle': 'شاحنة طويلة (بارجة) 18-19م',
      'estimatedTime': '6 ساعات',
      'currentLocation': 'تكريت - الطريق السريع',
      'startTime': '07:00',
      'estimatedArrival': '19:30',
      'distance': '465 كم',
      'completedDistance': '140 كم',
      'customer': 'خالد حسن محمد',
      'phone': '07601234567',
      'driverLatitude': 34.6137,
      'driverLongitude': 43.6793,
      'currentSpeed': 75, // كم/ساعة
      'speedLimit': 90, // كم/ساعة
    },
    {
      'id': 'TRK-006',
      'orderId': 'ORD-006',
      'cargoType': 'مواد طبية',
      'from': 'بغداد - الكاظمية',
      'to': 'السليمانية - مركز المدينة',
      'status': 'متأخر',
      'progress': 0.25,
      'driver': 'عمار صالح محمد',
      'vehicle': 'شاحنة مبردة',
      'estimatedTime': '8 ساعات',
      'currentLocation': 'كركوك - الطريق العام',
      'startTime': '05:00',
      'estimatedArrival': '20:00',
      'distance': '320 كم',
      'completedDistance': '80 كم',
      'customer': 'د. ليلى أحمد',
      'phone': '07751234567',
      'driverLatitude': 35.4676,
      'driverLongitude': 44.3922,
      'currentSpeed': 105, // كم/ساعة
      'speedLimit': 90, // كم/ساعة
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _backgroundController = AnimationController(
      duration: const Duration(seconds: 35),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.linear,
    ));
  }

  void _startAnimations() {
    _fadeController.forward();
    _backgroundController.repeat();

    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _slideController.forward();
      }
    });

    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) {
        _scaleController.forward();
      }
    });
  }



  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _backgroundController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          _buildAnimatedBackground(),
          SafeArea(
            child: Column(
              children: [
                _buildCustomAppBar(),
                _buildSearchAndFilters(),
                Expanded(
                  child: _buildShipmentsList(),
                ),
              ],
            ),
          ),
          _buildFloatingActionButton(),
        ],
      ),
      bottomNavigationBar: CompanyBottomNavigation(
        currentIndex: 2, // التتبع
        onTap: (index) => CompanyBottomNavigation.handleNavigation(context, index),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Positioned.fill(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0F172A),
              Color(0xFF1E293B),
              Color(0xFF334155),
              Color(0xFF475569),
              Color(0xFF64748B),
            ],
            stops: [0.0, 0.3, 0.6, 0.8, 1.0],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return Row(
              children: [
                Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => Navigator.pop(context),
                      borderRadius: BorderRadius.circular(12),
                      child: const Icon(
                        Bootstrap.arrow_left,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        langManager.getText('تتبع الشحنات', 'Track Shipments'),
                        style: GoogleFonts.tajawal(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        langManager.getText('متابعة مسار الشحنات الحية', 'Live shipment tracking'),
                        style: GoogleFonts.tajawal(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: _showMapView,
                          borderRadius: BorderRadius.circular(12),
                          child: const Icon(
                            Bootstrap.map,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: _refreshTracking,
                          borderRadius: BorderRadius.circular(12),
                          child: const Icon(
                            Bootstrap.arrow_clockwise,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.black.withOpacity(0.4),
                    AppColors.primary.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.3),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.2),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: SizedBox(
                height: AppDimensions.searchFieldHeight,
                child: TextField(
                  controller: _searchController,
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                  style: GoogleFonts.tajawal(
                    color: Colors.white,
                    fontSize: AppDimensions.fontSizeM,
                    fontWeight: FontWeight.w500,
                  ),
                  decoration: InputDecoration(
                    hintText: langManager.getText('البحث في الشحنات النشطة...', 'Search active shipments...'),
                    hintStyle: GoogleFonts.tajawal(
                      color: Colors.white.withOpacity(0.6),
                      fontSize: AppDimensions.fontSizeM,
                    ),
                    prefixIcon: Container(
                      margin: const EdgeInsets.all(8),
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppColors.primary.withOpacity(0.3),
                            AppColors.info.withOpacity(0.2),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                        border: Border.all(
                          color: AppColors.primary.withOpacity(0.4),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        Bootstrap.search,
                        color: AppColors.primary,
                        size: AppDimensions.iconSizeS,
                      ),
                    ),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                            icon: Icon(
                              Bootstrap.x_circle,
                              color: AppColors.primary.withOpacity(0.8),
                              size: AppDimensions.iconSizeS,
                            ),
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: AppSpacing.inputPadding,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }



  Widget _buildShipmentsList() {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            List<Map<String, dynamic>> filteredShipments = _shipments.where((shipment) {
              // عرض فقط الطلبات "في الطريق" و "متأخر"
              bool statusMatch = shipment['status'] == 'في الطريق' || shipment['status'] == 'متأخر';
              bool searchMatch = _searchQuery.isEmpty ||
                  shipment['id'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
                  shipment['orderId'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
                  shipment['cargoType'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
                  shipment['from'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
                  shipment['to'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
                  shipment['driver'].toString().toLowerCase().contains(_searchQuery.toLowerCase());

              return statusMatch && searchMatch;
            }).toList();

            if (filteredShipments.isEmpty) {
              return _buildEmptyState(langManager);
            }

            return ListView.builder(
              padding: const EdgeInsets.only(bottom: 120),
              itemCount: filteredShipments.length,
              itemBuilder: (context, index) {
                final shipment = filteredShipments[index];
                return _buildShipmentCard(shipment, langManager);
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState(LanguageManager langManager) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Bootstrap.truck,
              color: Colors.white.withOpacity(0.7),
              size: 60,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            langManager.getText('لا توجد شحنات نشطة', 'No active shipments'),
            style: GoogleFonts.tajawal(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            langManager.getText('جميع الشحنات مكتملة أو في انتظار التنفيذ', 'All shipments are completed or pending'),
            style: GoogleFonts.tajawal(
              color: Colors.white.withOpacity(0.7),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShipmentCard(Map<String, dynamic> shipment, LanguageManager langManager) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1F2937).withOpacity(0.95),
            const Color(0xFF374151).withOpacity(0.9),
            _getStatusColor(shipment['status']).withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(shipment['status']).withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: _getStatusColor(shipment['status']).withOpacity(0.15),
            blurRadius: 8,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showShipmentDetails(shipment, langManager),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(14),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(shipment['status']).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        shipment['id'],
                        style: GoogleFonts.tajawal(
                          color: _getStatusColor(shipment['status']),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(shipment['status']),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        shipment['status'],
                        style: GoogleFonts.tajawal(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: AppColors.primary.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Icon(
                              Bootstrap.geo_alt,
                              color: Colors.white,
                              size: 12,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  langManager.getText('من', 'From'),
                                  style: GoogleFonts.tajawal(
                                    fontSize: 10,
                                    color: Colors.white.withOpacity(0.7),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  shipment['from'],
                                  style: GoogleFonts.tajawal(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: AppColors.success.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Icon(
                              Bootstrap.pin_map,
                              color: Colors.white,
                              size: 12,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  langManager.getText('إلى', 'To'),
                                  style: GoogleFonts.tajawal(
                                    fontSize: 10,
                                    color: Colors.white.withOpacity(0.7),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  shipment['to'],
                                  style: GoogleFonts.tajawal(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                if (shipment['status'] == 'في الطريق' || shipment['status'] == 'متأخر') ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          _getStatusColor(shipment['status']).withOpacity(0.1),
                          _getStatusColor(shipment['status']).withOpacity(0.05),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Icon(
                              Bootstrap.geo_alt_fill,
                              color: _getStatusColor(shipment['status']),
                              size: 14,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              langManager.getText('الموقع الحالي', 'Current Location'),
                              style: GoogleFonts.tajawal(
                                fontSize: 10,
                                color: Colors.white.withOpacity(0.7),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              shipment['currentLocation'],
                              style: GoogleFonts.tajawal(
                                fontSize: 11,
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: shipment['progress'],
                            child: Container(
                              decoration: BoxDecoration(
                                color: _getStatusColor(shipment['status']),
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 6),
                        Row(
                          children: [
                            Text(
                              '${shipment['completedDistance']} / ${shipment['distance']}',
                              style: GoogleFonts.tajawal(
                                fontSize: 10,
                                color: Colors.white.withOpacity(0.7),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              '${(shipment['progress'] * 100).toInt()}%',
                              style: GoogleFonts.tajawal(
                                fontSize: 12,
                                color: _getStatusColor(shipment['status']),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                ],
                Row(
                  children: [
                    Icon(
                      Bootstrap.person_check,
                      color: AppColors.success,
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    GestureDetector(
                      onTap: () => _showDriverInfo(shipment, langManager),
                      child: Text(
                        shipment['driver'],
                        style: GoogleFonts.tajawal(
                          fontSize: 11,
                          color: AppColors.success,
                          fontWeight: FontWeight.w600,
                          decoration: TextDecoration.underline,
                          decorationColor: AppColors.success,
                        ),
                      ),
                    ),
                    const Spacer(),
                    // عرض السرعة للطلبات في الطريق والمتأخرة
                    if ((shipment['status'] == 'في الطريق' || shipment['status'] == 'متأخر') &&
                        shipment['currentSpeed'] != null) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getSpeedColor(shipment['currentSpeed'], shipment['speedLimit']),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Bootstrap.speedometer,
                              color: Colors.white,
                              size: 10,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              '${shipment['currentSpeed']}',
                              style: GoogleFonts.tajawal(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    // زر الموقع المباشر للطلبات في الطريق والمتأخرة
                    if ((shipment['status'] == 'في الطريق' || shipment['status'] == 'متأخر') &&
                        shipment['driverLatitude'] != null &&
                        shipment['driverLongitude'] != null) ...[
                      _buildLiveLocationButton(shipment, langManager),
                      const SizedBox(width: 8),
                    ],
                    Icon(
                      Bootstrap.clock,
                      color: Colors.white.withOpacity(0.7),
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      shipment['estimatedTime'],
                      style: GoogleFonts.tajawal(
                        fontSize: 11,
                        color: Colors.white.withOpacity(0.7),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),


              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return Positioned(
      bottom: 100,
      right: 20,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return Container(
              height: 56,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.primary,
                    Color(0xFF2563EB),
                  ],
                ),
                borderRadius: BorderRadius.circular(28),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.4),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: _openMap,
                  borderRadius: BorderRadius.circular(28),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Icon(
                          Bootstrap.map,
                          color: Colors.white,
                          size: 18,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        langManager.getText('الخريطة', 'Map View'),
                        style: GoogleFonts.tajawal(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'في الطريق':
        return AppColors.info;
      case 'تم التسليم':
        return AppColors.success;
      case 'متأخر':
        return AppColors.warning;
      case 'ملغي':
        return Colors.red;
      default:
        return AppColors.primary;
    }
  }

  Widget _buildLiveLocationButton(Map<String, dynamic> shipment, LanguageManager langManager) {
    return Container(
      width: 24,
      height: 24,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _openDriverRealLocation(
            shipment['driverId'] ?? 'driver_${shipment['id']}',
            shipment['driver'],
          ),
          borderRadius: BorderRadius.circular(6),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.success.withOpacity(0.8),
                  AppColors.success,
                ],
              ),
              borderRadius: BorderRadius.circular(6),
              boxShadow: [
                BoxShadow(
                  color: AppColors.success.withOpacity(0.3),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Icon(
              Bootstrap.geo_alt_fill,
              color: Colors.white,
              size: 12,
            ),
          ),
        ),
      ),
    );
  }





  Future<void> _openDriverLocation(double latitude, double longitude, LanguageManager langManager) async {
    // محاولة فتح Google Maps أولاً
    final googleMapsUrl = 'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';

    // رابط بديل لتطبيقات الخرائط الأخرى
    final universalMapsUrl = 'geo:$latitude,$longitude';

    try {
      // محاولة فتح Google Maps
      if (await canLaunchUrl(Uri.parse(googleMapsUrl))) {
        await launchUrl(
          Uri.parse(googleMapsUrl),
          mode: LaunchMode.externalApplication,
        );
      }
      // محاولة فتح تطبيق خرائط آخر
      else if (await canLaunchUrl(Uri.parse(universalMapsUrl))) {
        await launchUrl(
          Uri.parse(universalMapsUrl),
          mode: LaunchMode.externalApplication,
        );
      }
      // إذا فشل كل شيء، عرض رسالة خطأ
      else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                langManager.getText('لا يمكن فتح تطبيق الخرائط', 'Cannot open maps application'),
                style: GoogleFonts.tajawal(),
              ),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      // في حالة حدوث خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              langManager.getText('حدث خطأ في فتح الخريطة', 'Error opening map'),
              style: GoogleFonts.tajawal(),
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// فتح موقع السائق الحقيقي في الخرائط الخارجية
  Future<void> _openDriverRealLocation(String driverId, String driverName) async {
    await DriverLocationService.openDriverRealLocation(
      context: context,
      driverId: driverId,
      driverName: driverName,
    );
  }





  void _showDriverInfo(Map<String, dynamic> shipment, LanguageManager langManager) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1F2937),
              Color(0xFF374151),
              Color(0xFF4B5563),
            ],
          ),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          border: Border.all(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.5),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // العنوان
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppColors.success.withOpacity(0.8),
                          AppColors.success,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.success.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      Bootstrap.person_circle,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          shipment['driver'],
                          style: GoogleFonts.tajawal(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          langManager.getText('سائق الشحنة', 'Shipment Driver'),
                          style: GoogleFonts.tajawal(
                            fontSize: 14,
                            color: Colors.white.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // معلومات السائق
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  children: [
                    _buildDriverInfoCard(
                      icon: Bootstrap.truck,
                      title: langManager.getText('نوع المركبة', 'Vehicle Type'),
                      value: shipment['vehicle'] ?? langManager.getText('غير محدد', 'Not specified'),
                      langManager: langManager,
                    ),
                    const SizedBox(height: 12),
                    _buildDriverInfoCard(
                      icon: Bootstrap.telephone,
                      title: langManager.getText('رقم الهاتف', 'Phone Number'),
                      value: '07701234567',
                      langManager: langManager,
                    ),
                    const SizedBox(height: 12),
                    _buildDriverInfoCard(
                      icon: Bootstrap.geo_alt_fill,
                      title: langManager.getText('الموقع الحالي', 'Current Location'),
                      value: shipment['currentLocation'],
                      langManager: langManager,
                    ),
                    const SizedBox(height: 12),
                    _buildDriverInfoCard(
                      icon: Bootstrap.clock,
                      title: langManager.getText('وقت الوصول المتوقع', 'Estimated Arrival'),
                      value: shipment['estimatedArrival'],
                      langManager: langManager,
                    ),
                    const SizedBox(height: 20),

                    // أزرار الإجراءات
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.pop(context);
                              // فتح تطبيق الهاتف
                            },
                            icon: Icon(Bootstrap.telephone, size: 16),
                            label: Text(
                              langManager.getText('اتصال', 'Call'),
                              style: GoogleFonts.tajawal(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.success,
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.pop(context);
                              _openDriverRealLocation(
                                shipment['driverId'] ?? 'driver_${shipment['id']}',
                                shipment['driver'],
                              );
                            },
                            icon: Icon(Bootstrap.geo_alt_fill, size: 16),
                            label: Text(
                              langManager.getText('الموقع الحقيقي', 'Real Location'),
                              style: GoogleFonts.tajawal(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.info,
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDriverInfoCard({
    required IconData icon,
    required String title,
    required String value,
    required LanguageManager langManager,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.tajawal(
                    fontSize: 11,
                    color: Colors.white.withOpacity(0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _refreshTracking() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          Provider.of<LanguageManager>(context, listen: false)
              .getText('تم تحديث بيانات التتبع', 'Tracking data refreshed'),
          style: GoogleFonts.tajawal(),
        ),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showMapView() {
    final langManager = Provider.of<LanguageManager>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.95,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // العنوان
              Row(
                children: [
                  Icon(Bootstrap.map, color: AppColors.primary, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      langManager.getText('خريطة تتبع الشحنات', 'Shipment Tracking Map'),
                      style: GoogleFonts.tajawal(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Bootstrap.x_lg),
                  ),
                ],
              ),
              const Divider(),

              // محاكاة الخريطة
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Stack(
                    children: [
                      // خلفية الخريطة
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          image: const DecorationImage(
                            image: NetworkImage('https://via.placeholder.com/800x600/E5E7EB/6B7280?text=Map+View'),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),

                      // عرض السائقين على الخريطة
                      ..._shipments.where((s) => s['status'] == 'في الطريق' || s['status'] == 'متأخر').map((shipment) {
                        return Positioned(
                          left: (shipment['driverLatitude'] - 30) * 10 + 100,
                          top: (36 - shipment['driverLongitude']) * 10 + 100,
                          child: GestureDetector(
                            onTap: () => _showDriverOnMap(shipment, langManager),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: _getSpeedColor(shipment['currentSpeed'], shipment['speedLimit']),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(color: Colors.white, width: 2),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(Bootstrap.truck, color: Colors.white, size: 16),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${shipment['currentSpeed']} كم/س',
                                    style: GoogleFonts.tajawal(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // مفاتيح الألوان
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildSpeedLegend(
                    color: AppColors.success,
                    label: langManager.getText('سرعة عادية', 'Normal Speed'),
                  ),
                  _buildSpeedLegend(
                    color: Colors.orange,
                    label: langManager.getText('سرعة عالية', 'High Speed'),
                  ),
                  _buildSpeedLegend(
                    color: AppColors.error,
                    label: langManager.getText('تجاوز السرعة', 'Speeding'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSpeedLegend({required Color color, required String label}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: GoogleFonts.tajawal(fontSize: 12),
        ),
      ],
    );
  }

  Color _getSpeedColor(int currentSpeed, int speedLimit) {
    if (currentSpeed > speedLimit) {
      return AppColors.error; // أحمر للسرعة الزائدة
    } else if (currentSpeed > speedLimit * 0.9) {
      return Colors.orange; // برتقالي للسرعة العالية
    } else {
      return AppColors.success; // أخضر للسرعة العادية
    }
  }

  void _showDriverOnMap(Map<String, dynamic> shipment, LanguageManager langManager) {
    final bool isOverSpeeding = shipment['currentSpeed'] > shipment['speedLimit'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Bootstrap.truck, color: AppColors.primary, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                shipment['driver'],
                style: GoogleFonts.tajawal(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow(
              icon: Bootstrap.speedometer,
              label: langManager.getText('السرعة الحالية', 'Current Speed'),
              value: '${shipment['currentSpeed']} كم/س',
              color: _getSpeedColor(shipment['currentSpeed'], shipment['speedLimit']),
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              icon: Bootstrap.exclamation_triangle,
              label: langManager.getText('الحد الأقصى للسرعة', 'Speed Limit'),
              value: '${shipment['speedLimit']} كم/س',
              color: Colors.grey[600]!,
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              icon: Bootstrap.geo_alt,
              label: langManager.getText('الموقع الحالي', 'Current Location'),
              value: shipment['currentLocation'],
              color: AppColors.info,
            ),
            if (isOverSpeeding) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.error.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Bootstrap.exclamation_triangle, color: AppColors.error, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        langManager.getText('تحذير: السائق يتجاوز الحد الأقصى للسرعة', 'Warning: Driver exceeding speed limit'),
                        style: GoogleFonts.tajawal(
                          color: AppColors.error,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          if (isOverSpeeding)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context);
                _sendSpeedWarning(shipment, langManager);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              icon: const Icon(Bootstrap.bell, color: Colors.white, size: 16),
              label: Text(
                langManager.getText('إرسال تنبيه', 'Send Alert'),
                style: GoogleFonts.tajawal(color: Colors.white),
              ),
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              langManager.getText('إغلاق', 'Close'),
              style: GoogleFonts.tajawal(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.tajawal(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: GoogleFonts.tajawal(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _sendSpeedWarning(Map<String, dynamic> shipment, LanguageManager langManager) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          langManager.getText('تم إرسال تنبيه السرعة إلى ${shipment['driver']}', 'Speed warning sent to ${shipment['driver']}'),
          style: GoogleFonts.tajawal(),
        ),
        backgroundColor: AppColors.warning,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: langManager.getText('تفاصيل', 'Details'),
          textColor: Colors.white,
          onPressed: () {
            _showWarningDetails(shipment, langManager);
          },
        ),
      ),
    );
  }

  void _showWarningDetails(Map<String, dynamic> shipment, LanguageManager langManager) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Bootstrap.bell, color: AppColors.warning, size: 24),
            const SizedBox(width: 12),
            Text(
              langManager.getText('تفاصيل التنبيه', 'Alert Details'),
              style: GoogleFonts.tajawal(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              langManager.getText('تم إرسال التنبيه التالي:', 'The following alert was sent:'),
              style: GoogleFonts.tajawal(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                langManager.getText(
                  'تحذير: أنت تتجاوز الحد الأقصى للسرعة المسموح به. السرعة الحالية: ${shipment['currentSpeed']} كم/س، الحد الأقصى: ${shipment['speedLimit']} كم/س. يرجى تقليل السرعة فوراً للحفاظ على السلامة.',
                  'Warning: You are exceeding the speed limit. Current speed: ${shipment['currentSpeed']} km/h, Speed limit: ${shipment['speedLimit']} km/h. Please reduce speed immediately for safety.'
                ),
                style: GoogleFonts.tajawal(fontSize: 14),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              langManager.getText('وقت الإرسال: ${DateTime.now().toString().split('.')[0]}', 'Sent at: ${DateTime.now().toString().split('.')[0]}'),
              style: GoogleFonts.tajawal(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              langManager.getText('موافق', 'OK'),
              style: GoogleFonts.tajawal(),
            ),
          ),
        ],
      ),
    );
  }

  void _openMap() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LiveTrackingMapScreen(
          shipments: _shipments.where((s) => s['status'] == 'في الطريق' || s['status'] == 'متأخر').toList(),
        ),
      ),
    );
  }

  void _showShipmentDetails(Map<String, dynamic> shipment, LanguageManager langManager) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1F2937),
              Color(0xFF374151),
              Color(0xFF4B5563),
            ],
          ),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          border: Border.all(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.5),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Text(
                    langManager.getText('تفاصيل الشحنة', 'Shipment Details'),
                    style: GoogleFonts.tajawal(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getStatusColor(shipment['status']),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      shipment['status'],
                      style: GoogleFonts.tajawal(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailItem(
                      label: langManager.getText('رقم التتبع', 'Tracking ID'),
                      value: shipment['id'],
                      icon: Bootstrap.hash,
                    ),
                    _buildDetailItem(
                      label: langManager.getText('رقم الطلب', 'Order ID'),
                      value: shipment['orderId'],
                      icon: Bootstrap.receipt,
                    ),
                    _buildDetailItem(
                      label: langManager.getText('نوع البضاعة', 'Cargo Type'),
                      value: shipment['cargoType'],
                      icon: Bootstrap.box,
                    ),
                    _buildDetailItem(
                      label: langManager.getText('السائق', 'Driver'),
                      value: shipment['driver'],
                      icon: Bootstrap.person_check,
                    ),
                    _buildDetailItem(
                      label: langManager.getText('نوع المركبة', 'Vehicle Type'),
                      value: shipment['vehicle'],
                      icon: Bootstrap.truck,
                    ),
                    _buildDetailItem(
                      label: langManager.getText('المسافة الإجمالية', 'Total Distance'),
                      value: shipment['distance'],
                      icon: Bootstrap.speedometer,
                    ),
                    _buildDetailItem(
                      label: langManager.getText('المسافة المقطوعة', 'Completed Distance'),
                      value: shipment['completedDistance'],
                      icon: Bootstrap.check_circle,
                    ),
                    _buildDetailItem(
                      label: langManager.getText('وقت البدء', 'Start Time'),
                      value: shipment['startTime'],
                      icon: Bootstrap.clock,
                    ),
                    _buildDetailItem(
                      label: langManager.getText('الوصول المتوقع', 'Estimated Arrival'),
                      value: shipment['estimatedArrival'],
                      icon: Bootstrap.calendar_check,
                    ),
                    const SizedBox(height: 100),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem({
    required String label,
    required String value,
    required IconData icon,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

