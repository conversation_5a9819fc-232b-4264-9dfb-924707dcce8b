import 'package:flutter/foundation.dart';

/// تعداد أنواع الإشعارات
enum NotificationType {
  orderRequest,     // طلب جديد
  orderUpdate,      // تحديث الطلب
  orderAccepted,    // قبول الطلب
  orderCompleted,   // إكمال الطلب
  orderCancelled,   // إلغاء الطلب
  driverAssigned,   // تعيين سائق
  locationUpdate,   // تحديث الموقع
  paymentReceived,  // استلام دفعة
  systemAlert,      // تنبيه النظام
  promotion,        // عرض ترويجي
}

/// تعداد أولوية الإشعار
enum NotificationPriority {
  low,      // منخفضة
  normal,   // عادية
  high,     // عالية
  urgent,   // عاجلة
}

/// نموذج الإشعار
class AppNotification {
  final String id;
  final String userId;
  final NotificationType type;
  final NotificationPriority priority;
  final String title;
  final String message;
  final Map<String, dynamic>? data;
  final DateTime createdAt;
  final bool isRead;
  final bool isActionable;
  final String? actionUrl;
  final String? imageUrl;

  const AppNotification({
    required this.id,
    required this.userId,
    required this.type,
    required this.priority,
    required this.title,
    required this.message,
    this.data,
    required this.createdAt,
    this.isRead = false,
    this.isActionable = false,
    this.actionUrl,
    this.imageUrl,
  });

  /// إنشاء إشعار من JSON
  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      type: NotificationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NotificationType.systemAlert,
      ),
      priority: NotificationPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => NotificationPriority.normal,
      ),
      title: json['title'] as String,
      message: json['message'] as String,
      data: json['data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      isRead: json['is_read'] as bool? ?? false,
      isActionable: json['is_actionable'] as bool? ?? false,
      actionUrl: json['action_url'] as String?,
      imageUrl: json['image_url'] as String?,
    );
  }

  /// تحويل الإشعار إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type.name,
      'priority': priority.name,
      'title': title,
      'message': message,
      'data': data,
      'created_at': createdAt.toIso8601String(),
      'is_read': isRead,
      'is_actionable': isActionable,
      'action_url': actionUrl,
      'image_url': imageUrl,
    };
  }

  /// إنشاء نسخة محدثة من الإشعار
  AppNotification copyWith({
    String? id,
    String? userId,
    NotificationType? type,
    NotificationPriority? priority,
    String? title,
    String? message,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    bool? isRead,
    bool? isActionable,
    String? actionUrl,
    String? imageUrl,
  }) {
    return AppNotification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      title: title ?? this.title,
      message: message ?? this.message,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      isActionable: isActionable ?? this.isActionable,
      actionUrl: actionUrl ?? this.actionUrl,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  /// الحصول على اسم نوع الإشعار
  String get typeDisplayName {
    switch (type) {
      case NotificationType.orderRequest:
        return 'طلب جديد';
      case NotificationType.orderUpdate:
        return 'تحديث الطلب';
      case NotificationType.orderAccepted:
        return 'قبول الطلب';
      case NotificationType.orderCompleted:
        return 'إكمال الطلب';
      case NotificationType.orderCancelled:
        return 'إلغاء الطلب';
      case NotificationType.driverAssigned:
        return 'تعيين سائق';
      case NotificationType.locationUpdate:
        return 'تحديث الموقع';
      case NotificationType.paymentReceived:
        return 'استلام دفعة';
      case NotificationType.systemAlert:
        return 'تنبيه النظام';
      case NotificationType.promotion:
        return 'عرض ترويجي';
    }
  }

  /// الحصول على اسم أولوية الإشعار
  String get priorityDisplayName {
    switch (priority) {
      case NotificationPriority.low:
        return 'منخفضة';
      case NotificationPriority.normal:
        return 'عادية';
      case NotificationPriority.high:
        return 'عالية';
      case NotificationPriority.urgent:
        return 'عاجلة';
    }
  }

  /// التحقق من كون الإشعار جديد (أقل من 24 ساعة)
  bool get isNew {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inHours < 24;
  }

  /// التحقق من كون الإشعار عاجل
  bool get isUrgent {
    return priority == NotificationPriority.urgent;
  }

  /// الحصول على النص المختصر للرسالة
  String get shortMessage {
    if (message.length <= 100) return message;
    return '${message.substring(0, 97)}...';
  }

  @override
  String toString() {
    return 'AppNotification(id: $id, type: ${type.name}, title: $title)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
