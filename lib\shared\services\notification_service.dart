import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'supabase_service.dart';

/// تعداد أنواع الإشعارات
enum NotificationType {
  orderRequest,     // طلب جديد
  orderUpdate,      // تحديث الطلب
  orderAccepted,    // قبول الطلب
  orderCompleted,   // إكمال الطلب
  orderCancelled,   // إلغاء الطلب
  driverAssigned,   // تعيين سائق
  locationUpdate,   // تحديث الموقع
  paymentReceived,  // استلام دفعة
  systemAlert,      // تنبيه النظام
  promotion,        // عرض ترويجي
}

/// تعداد أولوية الإشعار
enum NotificationPriority {
  low,      // منخفضة
  normal,   // عادية
  high,     // عالية
  urgent,   // عاجلة
}

/// نموذج الإشعار
class AppNotification {
  final String id;
  final String userId;
  final NotificationType type;
  final NotificationPriority priority;
  final String title;
  final String message;
  final Map<String, dynamic>? data;
  final DateTime createdAt;
  final bool isRead;
  final bool isActionable;
  final String? actionUrl;
  final String? imageUrl;

  const AppNotification({
    required this.id,
    required this.userId,
    required this.type,
    this.priority = NotificationPriority.normal,
    required this.title,
    required this.message,
    this.data,
    required this.createdAt,
    this.isRead = false,
    this.isActionable = false,
    this.actionUrl,
    this.imageUrl,
  });

  /// إنشاء إشعار من JSON
  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      type: NotificationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NotificationType.systemAlert,
      ),
      priority: NotificationPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => NotificationPriority.normal,
      ),
      title: json['title'] as String,
      message: json['message'] as String,
      data: json['data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      isRead: json['is_read'] as bool? ?? false,
      isActionable: json['is_actionable'] as bool? ?? false,
      actionUrl: json['action_url'] as String?,
      imageUrl: json['image_url'] as String?,
    );
  }

  /// تحويل الإشعار إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type.name,
      'priority': priority.name,
      'title': title,
      'message': message,
      'data': data,
      'created_at': createdAt.toIso8601String(),
      'is_read': isRead,
      'is_actionable': isActionable,
      'action_url': actionUrl,
      'image_url': imageUrl,
    };
  }

  /// إنشاء نسخة محدثة من الإشعار
  AppNotification copyWith({
    String? id,
    String? userId,
    NotificationType? type,
    NotificationPriority? priority,
    String? title,
    String? message,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    bool? isRead,
    bool? isActionable,
    String? actionUrl,
    String? imageUrl,
  }) {
    return AppNotification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      title: title ?? this.title,
      message: message ?? this.message,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      isActionable: isActionable ?? this.isActionable,
      actionUrl: actionUrl ?? this.actionUrl,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  /// الحصول على اسم نوع الإشعار
  String get typeDisplayName {
    switch (type) {
      case NotificationType.orderRequest:
        return 'طلب جديد';
      case NotificationType.orderUpdate:
        return 'تحديث الطلب';
      case NotificationType.orderAccepted:
        return 'قبول الطلب';
      case NotificationType.orderCompleted:
        return 'إكمال الطلب';
      case NotificationType.orderCancelled:
        return 'إلغاء الطلب';
      case NotificationType.driverAssigned:
        return 'تعيين سائق';
      case NotificationType.locationUpdate:
        return 'تحديث الموقع';
      case NotificationType.paymentReceived:
        return 'استلام دفعة';
      case NotificationType.systemAlert:
        return 'تنبيه النظام';
      case NotificationType.promotion:
        return 'عرض ترويجي';
    }
  }

  @override
  String toString() {
    return 'AppNotification(id: $id, type: ${type.name}, title: $title)';
  }
}

/// خدمة الإشعارات
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // خدمة Supabase
  final SupabaseService _supabaseService = SupabaseService();

  // قائمة الإشعارات المحاكية (للاحتياط)
  final List<AppNotification> _notifications = [];

  // Controller للتحديثات المباشرة
  final StreamController<List<AppNotification>> _notificationsController =
      StreamController<List<AppNotification>>.broadcast();

  // Stream للاستماع للإشعارات
  Stream<List<AppNotification>> get notificationsStream => _notificationsController.stream;

  // هل نستخدم قاعدة البيانات الحقيقية
  bool _useRealDatabase = true;

  /// تهيئة الإشعارات الوهمية
  Future<void> initializeMockNotifications() async {
    await _generateMockNotifications();
  }

  /// إنشاء إشعارات وهمية
  Future<void> _generateMockNotifications() async {
    final random = Random();
    final now = DateTime.now();
    
    final mockNotifications = [
      AppNotification(
        id: 'notif_1',
        userId: 'driver_1',
        type: NotificationType.orderRequest,
        priority: NotificationPriority.high,
        title: 'طلب نقل جديد',
        message: 'لديك طلب نقل جديد من بغداد إلى البصرة',
        data: {'orderId': 'order_1', 'distance': '420 كم'},
        createdAt: now.subtract(const Duration(minutes: 5)),
        isActionable: true,
      ),
      AppNotification(
        id: 'notif_2',
        userId: 'company_1',
        type: NotificationType.orderAccepted,
        priority: NotificationPriority.normal,
        title: 'تم قبول الطلب',
        message: 'قام السائق أحمد محمد بقبول طلبكم رقم ORD-001',
        data: {'orderId': 'order_1', 'driverId': 'driver_1'},
        createdAt: now.subtract(const Duration(hours: 1)),
      ),
      AppNotification(
        id: 'notif_3',
        userId: 'driver_1',
        type: NotificationType.orderUpdate,
        priority: NotificationPriority.normal,
        title: 'تحديث الطلب',
        message: 'تم تحديث تفاصيل الطلب رقم ORD-001',
        data: {'orderId': 'order_1'},
        createdAt: now.subtract(const Duration(hours: 2)),
        isRead: true,
      ),
      AppNotification(
        id: 'notif_4',
        userId: 'company_1',
        type: NotificationType.locationUpdate,
        priority: NotificationPriority.low,
        title: 'تحديث الموقع',
        message: 'السائق الآن في الناصرية - وسط المدينة',
        data: {'orderId': 'order_1', 'location': 'الناصرية'},
        createdAt: now.subtract(const Duration(hours: 3)),
      ),
      AppNotification(
        id: 'notif_5',
        userId: 'driver_1',
        type: NotificationType.paymentReceived,
        priority: NotificationPriority.high,
        title: 'استلام دفعة',
        message: 'تم إيداع 75,000 د.ع في محفظتك',
        data: {'amount': 75000, 'orderId': 'order_1'},
        createdAt: now.subtract(const Duration(days: 1)),
      ),
      AppNotification(
        id: 'notif_6',
        userId: 'driver_1',
        type: NotificationType.systemAlert,
        priority: NotificationPriority.urgent,
        title: 'تحديث مطلوب',
        message: 'يرجى تحديث التطبيق للحصول على أحدث المميزات',
        data: {'version': '2.1.0'},
        createdAt: now.subtract(const Duration(days: 2)),
        isActionable: true,
        actionUrl: '/update',
      ),
      AppNotification(
        id: 'notif_7',
        userId: 'company_1',
        type: NotificationType.promotion,
        priority: NotificationPriority.low,
        title: 'عرض خاص',
        message: 'خصم 20% على جميع رحلات النقل هذا الأسبوع',
        data: {'discount': 20, 'validUntil': '2024-02-15'},
        createdAt: now.subtract(const Duration(days: 3)),
        imageUrl: 'assets/images/promotion_banner.png',
      ),
    ];

    _notifications.addAll(mockNotifications);
    _notificationsController.add(_notifications);
  }

  /// الحصول على إشعارات المستخدم
  Future<List<AppNotification>> getNotificationsForUser(String userId) async {
    try {
      if (_useRealDatabase) {
        // جلب الإشعارات من قاعدة البيانات
        final notificationsData = await _supabaseService.getUserNotifications(userId);
        final notifications = notificationsData.map((data) => AppNotification.fromJson(data)).toList();
        return notifications;
      } else {
        // استخدام البيانات المحاكية
        return _notifications
            .where((notification) => notification.userId == userId)
            .toList()
          ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
      }
    } catch (e) {
      debugPrint('خطأ في جلب الإشعارات، التحول للبيانات المحاكية: $e');
      _useRealDatabase = false;
      return _notifications
          .where((notification) => notification.userId == userId)
          .toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  Future<int> getUnreadCount(String userId) async {
    return _notifications
        .where((notification) => 
            notification.userId == userId && !notification.isRead)
        .length;
  }

  /// إرسال إشعار جديد
  Future<void> sendNotification(AppNotification notification) async {
    try {
      if (_useRealDatabase) {
        // حفظ الإشعار في قاعدة البيانات
        await _supabaseService.sendNotification(notification.toJson());
        debugPrint('تم إرسال إشعار إلى قاعدة البيانات: ${notification.title}');
      } else {
        // إضافة للبيانات المحاكية
        _notifications.add(notification);
        _notificationsController.add(_notifications);
        debugPrint('تم إرسال إشعار محاكي: ${notification.title}');
      }
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار، التحول للبيانات المحاكية: $e');
      _useRealDatabase = false;
      _notifications.add(notification);
      _notificationsController.add(_notifications);
    }
  }

  /// تحديد إشعار كمقروء
  Future<void> markAsRead(String notificationId) async {
    try {
      if (_useRealDatabase) {
        // تحديث في قاعدة البيانات
        await _supabaseService.markNotificationAsRead(notificationId);
      } else {
        // تحديث في البيانات المحاكية
        final index = _notifications.indexWhere((n) => n.id == notificationId);
        if (index != -1) {
          _notifications[index] = _notifications[index].copyWith(isRead: true);
          _notificationsController.add(_notifications);
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحديد الإشعار كمقروء: $e');
      _useRealDatabase = false;
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        _notificationsController.add(_notifications);
      }
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<void> markAllAsRead(String userId) async {
    for (int i = 0; i < _notifications.length; i++) {
      if (_notifications[i].userId == userId && !_notifications[i].isRead) {
        _notifications[i] = _notifications[i].copyWith(isRead: true);
      }
    }
    _notificationsController.add(_notifications);
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    _notifications.removeWhere((n) => n.id == notificationId);
    _notificationsController.add(_notifications);
  }

  /// حذف جميع الإشعارات المقروءة
  Future<void> deleteReadNotifications(String userId) async {
    _notifications.removeWhere((n) => n.userId == userId && n.isRead);
    _notificationsController.add(_notifications);
  }

  /// إنشاء إشعار طلب جديد للسائق
  Future<void> notifyDriverOfNewOrder(String driverId, String orderId, String orderDetails) async {
    final notification = AppNotification(
      id: 'notif_${DateTime.now().millisecondsSinceEpoch}',
      userId: driverId,
      type: NotificationType.orderRequest,
      priority: NotificationPriority.high,
      title: 'طلب نقل جديد',
      message: 'لديك طلب نقل جديد: $orderDetails',
      data: {'orderId': orderId},
      createdAt: DateTime.now(),
      isActionable: true,
    );
    
    await sendNotification(notification);
  }

  /// إنشاء إشعار قبول الطلب للشركة
  Future<void> notifyCompanyOfOrderAcceptance(String companyId, String orderId, String driverName) async {
    final notification = AppNotification(
      id: 'notif_${DateTime.now().millisecondsSinceEpoch}',
      userId: companyId,
      type: NotificationType.orderAccepted,
      priority: NotificationPriority.normal,
      title: 'تم قبول الطلب',
      message: 'قام السائق $driverName بقبول طلبكم رقم $orderId',
      data: {'orderId': orderId},
      createdAt: DateTime.now(),
    );
    
    await sendNotification(notification);
  }

  /// إنشاء إشعار تحديث الموقع
  Future<void> notifyLocationUpdate(String userId, String orderId, String location) async {
    final notification = AppNotification(
      id: 'notif_${DateTime.now().millisecondsSinceEpoch}',
      userId: userId,
      type: NotificationType.locationUpdate,
      priority: NotificationPriority.low,
      title: 'تحديث الموقع',
      message: 'الموقع الحالي: $location',
      data: {'orderId': orderId, 'location': location},
      createdAt: DateTime.now(),
    );
    
    await sendNotification(notification);
  }

  /// تنظيف الموارد
  void dispose() {
    _notificationsController.close();
  }
}
