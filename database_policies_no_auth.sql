-- ═══════════════════════════════════════════════════════════════
-- سياسات الأمان المحدثة لتطبيق حمولتي (بدون Supabase Auth)
-- ═══════════════════════════════════════════════════════════════

-- ملاحظة: هذه السياسات تعمل بدون Supabase Auth
-- يتم التحكم في الوصول من خلال التطبيق نفسه

-- ═══════════════════════════════════════════════════════════════
-- إزالة السياسات القديمة
-- ═══════════════════════════════════════════════════════════════

-- إزالة سياسات المستخدمين
DROP POLICY IF EXISTS "Users can view own data" ON users;
DROP POLICY IF EXISTS "Users can update own data" ON users;
DROP POLICY IF EXISTS "Users can insert own data" ON users;

-- إزالة سياسات السائقين
DROP POLICY IF EXISTS "Drivers can view own data" ON drivers;
DROP POLICY IF EXISTS "Drivers can update own data" ON drivers;
DROP POLICY IF EXISTS "Drivers can insert own data" ON drivers;
DROP POLICY IF EXISTS "Companies can view available drivers" ON drivers;

-- إزالة سياسات الشركات
DROP POLICY IF EXISTS "Companies can view own data" ON companies;
DROP POLICY IF EXISTS "Companies can update own data" ON companies;
DROP POLICY IF EXISTS "Companies can insert own data" ON companies;

-- إزالة سياسات الطلبات
DROP POLICY IF EXISTS "Companies can view own orders" ON orders;
DROP POLICY IF EXISTS "Drivers can view assigned orders" ON orders;
DROP POLICY IF EXISTS "Drivers can view available orders" ON orders;
DROP POLICY IF EXISTS "Companies can insert orders" ON orders;
DROP POLICY IF EXISTS "Companies can update own orders" ON orders;
DROP POLICY IF EXISTS "Drivers can update assigned orders" ON orders;

-- إزالة سياسات تاريخ الطلبات
DROP POLICY IF EXISTS "Users can view order history" ON order_status_history;
DROP POLICY IF EXISTS "System can insert order history" ON order_status_history;

-- إزالة سياسات الإشعارات
DROP POLICY IF EXISTS "Users can view own notifications" ON notifications;
DROP POLICY IF EXISTS "Users can update own notifications" ON notifications;
DROP POLICY IF EXISTS "System can insert notifications" ON notifications;

-- ═══════════════════════════════════════════════════════════════
-- تعطيل Row Level Security مؤقتاً
-- ═══════════════════════════════════════════════════════════════

ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE drivers DISABLE ROW LEVEL SECURITY;
ALTER TABLE companies DISABLE ROW LEVEL SECURITY;
ALTER TABLE orders DISABLE ROW LEVEL SECURITY;
ALTER TABLE order_status_history DISABLE ROW LEVEL SECURITY;
ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;

-- ═══════════════════════════════════════════════════════════════
-- سياسات أمان مبسطة (اختيارية)
-- ═══════════════════════════════════════════════════════════════

-- يمكن تفعيل هذه السياسات لاحقاً عند الحاجة لمزيد من الأمان

/*
-- تفعيل RLS مرة أخرى
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- سياسات مبسطة للقراءة والكتابة
CREATE POLICY "Allow all operations" ON users FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations" ON drivers FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations" ON companies FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations" ON orders FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations" ON order_status_history FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations" ON notifications FOR ALL USING (true) WITH CHECK (true);
*/

-- ═══════════════════════════════════════════════════════════════
-- سياسات Storage محدثة
-- ═══════════════════════════════════════════════════════════════

-- إزالة السياسات القديمة
DROP POLICY IF EXISTS "Public Access for Profile Images" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload profile images" ON storage.objects;
DROP POLICY IF EXISTS "Users can update profile images" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete profile images" ON storage.objects;

DROP POLICY IF EXISTS "Public Access for Vehicle Images" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can upload vehicle images" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can update vehicle images" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can delete vehicle images" ON storage.objects;

DROP POLICY IF EXISTS "Order participants can view order images" ON storage.objects;
DROP POLICY IF EXISTS "Companies can upload order images" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can upload images for assigned orders" ON storage.objects;
DROP POLICY IF EXISTS "Companies can update order images" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can update images for assigned orders" ON storage.objects;
DROP POLICY IF EXISTS "Companies can delete order images" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can delete images for assigned orders" ON storage.objects;

-- سياسات Storage مبسطة
CREATE POLICY "Allow public access to all images" ON storage.objects FOR SELECT USING (true);
CREATE POLICY "Allow authenticated uploads" ON storage.objects FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow authenticated updates" ON storage.objects FOR UPDATE USING (true);
CREATE POLICY "Allow authenticated deletes" ON storage.objects FOR DELETE USING (true);

-- ═══════════════════════════════════════════════════════════════
-- تعليقات مهمة
-- ═══════════════════════════════════════════════════════════════

/*
ملاحظات مهمة:

1. تم تعطيل Row Level Security لجميع الجداول
2. التحكم في الوصول يتم الآن من خلال التطبيق نفسه
3. سياسات Storage تسمح بالوصول الكامل للمستخدمين المصادق عليهم
4. يمكن تفعيل سياسات أكثر تقييداً لاحقاً عند الحاجة

للعودة لسياسات أكثر أماناً:
1. فعّل RLS مرة أخرى
2. أنشئ سياسات مخصصة حسب احتياجات التطبيق
3. استخدم دوال مخصصة للتحقق من الصلاحيات

مثال على دالة مخصصة للتحقق:
CREATE OR REPLACE FUNCTION check_user_access(user_id UUID, resource_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- منطق التحقق من الصلاحيات
  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
*/
