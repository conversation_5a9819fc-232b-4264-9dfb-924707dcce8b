import 'dart:async';
import 'dart:math';
import 'package:geolocator/geolocator.dart';
import '../models/user.dart';
import '../models/driver.dart';
import '../models/company.dart';
import '../models/order.dart';

/// خدمة البيانات المحاكية - ستُستبدل بقاعدة البيانات الحقيقية
class DataService {
  static final DataService _instance = DataService._internal();
  factory DataService() => _instance;
  DataService._internal();

  // البيانات المحاكية
  final List<Driver> _drivers = [];
  final List<Company> _companies = [];
  final List<Order> _orders = [];
  
  // المستخدم الحالي
  User? _currentUser;
  
  // Controllers للتحديثات المباشرة
  final StreamController<List<Driver>> _driversController = StreamController<List<Driver>>.broadcast();
  final StreamController<List<Order>> _ordersController = StreamController<List<Order>>.broadcast();
  final StreamController<User?> _userController = StreamController<User?>.broadcast();

  // Streams للاستماع للتحديثات
  Stream<List<Driver>> get driversStream => _driversController.stream;
  Stream<List<Order>> get ordersStream => _ordersController.stream;
  Stream<User?> get userStream => _userController.stream;

  /// تهيئة البيانات الوهمية
  Future<void> initializeMockData() async {
    await _generateMockDrivers();
    await _generateMockCompanies();
    await _generateMockOrders();
  }

  /// إنشاء سائقين وهميين
  Future<void> _generateMockDrivers() async {
    final vehicleTypes = [
      'أتيكو 6 متر',
      'أتيكو 10 متر',
      'شاحنة جادر متحرك 13.60م',
      'شاحنة فلات (سطحة) 13.60م',
      'شاحنة جوانب مفتوحة',
      'شاحنة طويلة (بارجة) 18-19م',
      'شاحنة مبردة 13.60م',
      'شاحنة صهريج (مشتقات نفطية)',
      'شاحنة صهريج غاز أو LPG',
      'شاحنة طويلة (بارجة سطحة)',
      'حاوية 40 قدم',
    ];

    final names = [
      'أحمد محمد علي',
      'محمد حسن أحمد',
      'علي حسين محمد',
      'حسن أحمد علي',
      'سعد محمود حسن',
      'عمار صالح محمد',
      'كريم عبدالله أحمد',
      'يوسف إبراهيم علي',
    ];

    final random = Random();
    
    for (int i = 0; i < 8; i++) {
      final driver = Driver(
        id: 'driver_${i + 1}',
        name: names[i],
        email: 'driver${i + 1}@hamoolati.com',
        phone: '0770${1234567 + i}',
        createdAt: DateTime.now().subtract(Duration(days: random.nextInt(365))),
        vehicleType: vehicleTypes[random.nextInt(vehicleTypes.length)],
        plateNumber: 'بغداد ${12345 + i}',
        vehicleModel: ['إيسوزو', 'مرسيدس', 'فولفو', 'سكانيا'][random.nextInt(4)] + ' ${2020 + random.nextInt(4)}',
        manufacturingYear: 2020 + random.nextInt(4),
        drivingLicenseNumber: 'DL${100000 + i}',
        licenseExpiryDate: DateTime.now().add(Duration(days: 365 + random.nextInt(1095))),
        rating: 4.0 + random.nextDouble(),
        totalTrips: random.nextInt(1000) + 100,
        totalEarnings: (random.nextInt(50000) + 10000).toDouble(),
        driverStatus: DriverStatus.values[random.nextInt(DriverStatus.values.length)],
        currentLocation: Position(
          latitude: 33.3152 + (random.nextDouble() - 0.5) * 0.1,
          longitude: 44.3661 + (random.nextDouble() - 0.5) * 0.1,
          timestamp: DateTime.now(),
          accuracy: 10.0,
          altitude: 0.0,
          heading: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
          altitudeAccuracy: 0.0,
          headingAccuracy: 0.0,
        ),
        lastLocationUpdate: DateTime.now().subtract(Duration(minutes: random.nextInt(60))),
        serviceAreas: ['بغداد', 'البصرة', 'أربيل'],
        emergencyContact: EmergencyContact(
          name: 'فاطمة علي',
          phone: '0771${1234567 + i}',
          relation: 'الزوجة',
        ),
      );
      _drivers.add(driver);
    }
    
    _driversController.add(_drivers);
  }

  /// إنشاء شركات وهمية
  Future<void> _generateMockCompanies() async {
    final companyNames = [
      'شركة النقل السريع',
      'مؤسسة الشحن المتقدم',
      'شركة اللوجستيات الذكية',
      'مجموعة النقل الحديث',
    ];

    final random = Random();
    
    for (int i = 0; i < 4; i++) {
      final company = Company(
        id: 'company_${i + 1}',
        name: companyNames[i],
        email: 'company${i + 1}@hamoolati.com',
        phone: '0780${1234567 + i}',
        createdAt: DateTime.now().subtract(Duration(days: random.nextInt(730))),
        businessLicense: 'BL${100000 + i}',
        address: 'بغداد - ${['الكرادة', 'الجادرية', 'الكاظمية', 'الأعظمية'][i]}',
        website: 'www.company${i + 1}.com',
        description: 'شركة متخصصة في خدمات النقل واللوجستيات',
        serviceAreas: ['بغداد', 'البصرة', 'أربيل', 'النجف'],
        vehicleTypes: [
          'أتيكو 6 متر',
          'أتيكو 10 متر',
          'شاحنة جادر متحرك 13.60م',
        ],
        rating: 4.0 + random.nextDouble(),
        totalOrders: random.nextInt(5000) + 1000,
        activeOrders: random.nextInt(50) + 10,
        totalPayments: (random.nextInt(1000000) + 100000).toDouble(),
        subscriptionExpiry: DateTime.now().add(Duration(days: 365)),
        subscription: CompanySubscription.values[random.nextInt(CompanySubscription.values.length)],
      );
      _companies.add(company);
    }
  }

  /// إنشاء طلبات وهمية
  Future<void> _generateMockOrders() async {
    final cargoTypes = [
      'بضائع عامة',
      'مواد غذائية',
      'أجهزة إلكترونية',
      'أثاث ومنزلية',
      'مواد بناء',
      'مواد طبية',
    ];

    final locations = [
      {'name': 'بغداد - الكرادة', 'lat': 33.3152, 'lng': 44.3661},
      {'name': 'البصرة - الجبيلة', 'lat': 30.5085, 'lng': 47.7804},
      {'name': 'أربيل - مركز المدينة', 'lat': 36.1911, 'lng': 44.0093},
      {'name': 'النجف - وسط المدينة', 'lat': 32.0285, 'lng': 44.3419},
      {'name': 'كربلاء - الحسينية', 'lat': 32.6160, 'lng': 44.0242},
    ];

    final random = Random();
    
    for (int i = 0; i < 20; i++) {
      final pickupLocation = locations[random.nextInt(locations.length)];
      final deliveryLocation = locations[random.nextInt(locations.length)];
      
      final order = Order(
        id: 'order_${i + 1}',
        companyId: _companies[random.nextInt(_companies.length)].id,
        driverId: random.nextBool() ? _drivers[random.nextInt(_drivers.length)].id : null,
        cargoType: cargoTypes[random.nextInt(cargoTypes.length)],
        weight: (random.nextInt(50) + 1).toDouble(),
        vehicleType: [
          'أتيكو 6 متر',
          'أتيكو 10 متر',
          'شاحنة جادر متحرك 13.60م',
        ][random.nextInt(3)],
        status: OrderStatus.values[random.nextInt(OrderStatus.values.length)],
        priority: OrderPriority.values[random.nextInt(OrderPriority.values.length)],
        paymentMethod: PaymentMethod.values[random.nextInt(PaymentMethod.values.length)],
        price: (random.nextInt(500000) + 50000).toDouble(),
        createdAt: DateTime.now().subtract(Duration(days: random.nextInt(30))),
        scheduledPickupTime: DateTime.now().add(Duration(hours: random.nextInt(48))),
        scheduledDeliveryTime: DateTime.now().add(Duration(hours: random.nextInt(72) + 48)),
        pickupLocation: OrderLocation(
          latitude: pickupLocation['lat']! as double,
          longitude: pickupLocation['lng']! as double,
          address: pickupLocation['name']! as String,
          contactName: 'أحمد محمد',
          contactPhone: '0770${1234567 + i}',
          instructions: 'يرجى الاتصال عند الوصول',
        ),
        deliveryLocation: OrderLocation(
          latitude: deliveryLocation['lat']! as double,
          longitude: deliveryLocation['lng']! as double,
          address: deliveryLocation['name']! as String,
          contactName: 'سعد حسين',
          contactPhone: '0771${1234567 + i}',
          instructions: 'التسليم في ساعات العمل فقط',
        ),
        description: 'طلب نقل ${cargoTypes[random.nextInt(cargoTypes.length)]}',
        specialInstructions: 'يرجى التعامل بحذر مع البضاعة',
      );
      _orders.add(order);
    }
    
    _ordersController.add(_orders);
  }

  /// الحصول على سائق محدد بالمعرف
  Future<Driver?> getDriverById(String driverId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    try {
      return _drivers.firstWhere((driver) => driver.id == driverId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على السائقين المتوفرين
  Future<List<Driver>> getAvailableDrivers({
    Position? nearLocation,
    double maxDistance = 50.0,
    String? vehicleType,
  }) async {
    var availableDrivers = _drivers.where((driver) => driver.isAvailable).toList();

    if (nearLocation != null) {
      availableDrivers = availableDrivers.where((driver) {
        final distance = driver.distanceFrom(nearLocation);
        return distance != null && distance <= maxDistance;
      }).toList();

      // ترتيب حسب المسافة
      availableDrivers.sort((a, b) {
        final distanceA = a.distanceFrom(nearLocation) ?? double.infinity;
        final distanceB = b.distanceFrom(nearLocation) ?? double.infinity;
        return distanceA.compareTo(distanceB);
      });
    }

    if (vehicleType != null) {
      availableDrivers = availableDrivers.where((driver) => driver.vehicleType == vehicleType).toList();
    }

    return availableDrivers;
  }

  /// الحصول على الطلبات للشركة
  Future<List<Order>> getOrdersForCompany(String companyId) async {
    return _orders.where((order) => order.companyId == companyId).toList();
  }

  /// الحصول على الطلبات للسائق
  Future<List<Order>> getOrdersForDriver(String driverId) async {
    return _orders.where((order) => order.driverId == driverId).toList();
  }

  /// الحصول على الطلبات المتاحة للسائق
  Future<List<Order>> getAvailableOrdersForDriver(String driverId) async {
    final driver = _drivers.firstWhere((d) => d.id == driverId);
    return _orders.where((order) => 
      order.status == OrderStatus.pending && 
      order.vehicleType == driver.vehicleType
    ).toList();
  }

  /// إنشاء طلب جديد
  Future<String> createOrder(Order order) async {
    _orders.add(order);
    _ordersController.add(_orders);
    return order.id;
  }

  /// تحديث حالة الطلب
  Future<void> updateOrderStatus(String orderId, OrderStatus status, {String? note}) async {
    final orderIndex = _orders.indexWhere((order) => order.id == orderId);
    if (orderIndex != -1) {
      final updatedOrder = _orders[orderIndex].copyWith(
        status: status,
        updatedAt: DateTime.now(),
      );
      _orders[orderIndex] = updatedOrder;
      _ordersController.add(_orders);
    }
  }

  /// تعيين سائق للطلب
  Future<void> assignDriverToOrder(String orderId, String driverId) async {
    final orderIndex = _orders.indexWhere((order) => order.id == orderId);
    if (orderIndex != -1) {
      final updatedOrder = _orders[orderIndex].copyWith(
        driverId: driverId,
        status: OrderStatus.accepted,
        updatedAt: DateTime.now(),
      );
      _orders[orderIndex] = updatedOrder;
      _ordersController.add(_orders);
    }
  }

  /// تحديث موقع السائق
  Future<void> updateDriverLocation(String driverId, Position position) async {
    final driverIndex = _drivers.indexWhere((driver) => driver.id == driverId);
    if (driverIndex != -1) {
      final updatedDriver = _drivers[driverIndex].copyWith(
        currentLocation: position,
        lastLocationUpdate: DateTime.now(),
      );
      _drivers[driverIndex] = updatedDriver;
      _driversController.add(_drivers);
    }
  }

  /// تحديث حالة السائق
  Future<void> updateDriverStatus(String driverId, DriverStatus status) async {
    final driverIndex = _drivers.indexWhere((driver) => driver.id == driverId);
    if (driverIndex != -1) {
      final updatedDriver = _drivers[driverIndex].copyWith(
        driverStatus: status,
        updatedAt: DateTime.now(),
      );
      _drivers[driverIndex] = updatedDriver;
      _driversController.add(_drivers);
    }
  }

  /// تسجيل الدخول
  Future<User?> login(String email, String password, UserType userType) async {
    // محاكاة تسجيل الدخول
    await Future.delayed(const Duration(seconds: 1));

    User? foundUser;

    if (userType == UserType.driver) {
      try {
        foundUser = _drivers.firstWhere(
          (driver) => driver.email == email && _validatePassword(password),
        );
      } catch (e) {
        // لم يتم العثور على المستخدم
        throw Exception('البريد الإلكتروني أو كلمة المرور غير صحيحة');
      }
    } else {
      try {
        foundUser = _companies.firstWhere(
          (company) => company.email == email && _validatePassword(password),
        );
      } catch (e) {
        // لم يتم العثور على المستخدم
        throw Exception('البريد الإلكتروني أو كلمة المرور غير صحيحة');
      }
    }

    _currentUser = foundUser;
    _userController.add(_currentUser);
    return _currentUser;
  }

  /// التحقق من صحة كلمة المرور (للبيانات الوهمية)
  bool _validatePassword(String password) {
    // للبيانات الوهمية، نقبل كلمات مرور محددة فقط
    const validPasswords = ['password', '123456', 'hamoolati2024'];
    return validPasswords.contains(password);
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    _currentUser = null;
    _userController.add(null);
  }

  /// الحصول على المستخدم الحالي
  User? get currentUser => _currentUser;

  /// تنظيف الموارد
  void dispose() {
    _driversController.close();
    _ordersController.close();
    _userController.close();
  }
}
