import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../models/order.dart';
import '../models/user.dart';
import '../services/data_service.dart';
import '../services/real_data_service.dart';
import '../services/real_data_service.dart';
import '../services/notification_service.dart';

/// حالة الطلبات
enum OrderState {
  initial,    // الحالة الأولية
  loading,    // جاري التحميل
  loaded,     // تم التحميل
  error,      // خطأ
}

/// مزود الطلبات
class OrderProvider extends ChangeNotifier {
  final DataService _dataService = DataService();
  final RealDataService _realDataService = RealDataService();
  final NotificationService _notificationService = NotificationService();
  
  // الحالة الحالية
  OrderState _state = OrderState.initial;
  List<Order> _orders = [];
  List<Order> _availableOrders = [];
  String? _errorMessage;
  
  // فلاتر البحث
  String _searchQuery = '';
  OrderStatus? _statusFilter;
  OrderPriority? _priorityFilter;
  String? _vehicleTypeFilter;

  // Getters
  OrderState get state => _state;
  List<Order> get orders => _getFilteredOrders();
  List<Order> get availableOrders => _availableOrders;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _state == OrderState.loading;
  String get searchQuery => _searchQuery;
  OrderStatus? get statusFilter => _statusFilter;
  OrderPriority? get priorityFilter => _priorityFilter;
  String? get vehicleTypeFilter => _vehicleTypeFilter;

  /// تهيئة المزود
  Future<void> initialize() async {
    _setState(OrderState.loading);
    
    try {
      // الاستماع لتحديثات الطلبات
      _dataService.ordersStream.listen((orders) {
        _orders = orders;
        _setState(OrderState.loaded);
      });
      
      _setState(OrderState.loaded);
    } catch (e) {
      _setError('خطأ في تهيئة الطلبات: $e');
    }
  }

  /// تحميل طلبات الشركة
  Future<void> loadCompanyOrders(String companyId) async {
    _setState(OrderState.loading);
    
    try {
      _orders = await _dataService.getOrdersForCompany(companyId);
      _setState(OrderState.loaded);
    } catch (e) {
      _setError('خطأ في تحميل طلبات الشركة: $e');
    }
  }

  /// تحميل طلبات السائق
  Future<void> loadDriverOrders(String driverId) async {
    _setState(OrderState.loading);
    
    try {
      _orders = await _dataService.getOrdersForDriver(driverId);
      _setState(OrderState.loaded);
    } catch (e) {
      _setError('خطأ في تحميل طلبات السائق: $e');
    }
  }

  /// تحميل الطلبات المتاحة للسائق
  Future<void> loadAvailableOrders(String driverId) async {
    try {
      _availableOrders = await _dataService.getAvailableOrdersForDriver(driverId);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل الطلبات المتاحة: $e');
    }
  }

  /// إنشاء طلب جديد
  Future<String?> createOrder({
    required String companyId,
    required String cargoType,
    required double weight,
    required String vehicleType,
    required OrderPriority priority,
    required PaymentMethod paymentMethod,
    required double price,
    required OrderLocation pickupLocation,
    required OrderLocation deliveryLocation,
    DateTime? scheduledPickupTime,
    DateTime? scheduledDeliveryTime,
    String? description,
    String? specialInstructions,
  }) async {
    _setState(OrderState.loading);
    
    try {
      final order = Order(
        id: 'order_${DateTime.now().millisecondsSinceEpoch}',
        companyId: companyId,
        cargoType: cargoType,
        weight: weight,
        vehicleType: vehicleType,
        priority: priority,
        paymentMethod: paymentMethod,
        price: price,
        createdAt: DateTime.now(),
        pickupLocation: pickupLocation,
        deliveryLocation: deliveryLocation,
        scheduledPickupTime: scheduledPickupTime,
        scheduledDeliveryTime: scheduledDeliveryTime,
        description: description,
        specialInstructions: specialInstructions,
      );

      final orderId = await _dataService.createOrder(order);
      
      // إشعار السائقين المناسبين
      await _notifyAvailableDrivers(order);
      
      _setState(OrderState.loaded);
      return orderId;
    } catch (e) {
      _setError('خطأ في إنشاء الطلب: $e');
      return null;
    }
  }

  /// قبول طلب من قبل السائق مع البيانات الحقيقية
  Future<bool> acceptOrder(String orderId, String driverId) async {
    try {
      // الحصول على بيانات الطلب والسائق الحقيقية
      final order = _orders.firstWhere((o) => o.id == orderId);

      // استخدام RealDataService للحصول على بيانات السائق
      final realDataService = RealDataService();
      final driver = await realDataService.getDriverById(driverId);

      if (driver == null) {
        throw Exception('لم يتم العثور على بيانات السائق');
      }

      // تعيين السائق للطلب في قاعدة البيانات
      await _dataService.assignDriverToOrder(orderId, driverId);

      // الحصول على الموقع الحالي للسائق
      String? locationName;
      if (driver.currentLocation != null) {
        // يمكن هنا استخدام خدمة Geocoding للحصول على اسم المكان
        locationName = await _getLocationName(
          driver.currentLocation!.latitude,
          driver.currentLocation!.longitude,
        );
      }

      // إشعار الشركة بقبول الطلب مع جميع التفاصيل
      await _notificationService.notifyCompanyOfOrderAcceptance(
        companyId: order.companyId,
        orderId: orderId,
        driverName: driver.name,
        driverId: driverId,
        driverPhone: driver.phone,
        driverLatitude: driver.currentLocation?.latitude,
        driverLongitude: driver.currentLocation?.longitude,
        driverLocation: locationName ?? 'موقع غير محدد',
        vehicleType: driver.vehicleType,
        plateNumber: driver.plateNumber,
      );

      // تحديث حالة الطلب محلياً
      final orderIndex = _orders.indexWhere((o) => o.id == orderId);
      if (orderIndex != -1) {
        _orders[orderIndex] = _orders[orderIndex].copyWith(
          driverId: driverId,
          status: OrderStatus.accepted,
          updatedAt: DateTime.now(),
        );
      }

      // إزالة الطلب من القائمة المتاحة
      _availableOrders.removeWhere((o) => o.id == orderId);
      notifyListeners();

      // إرسال تحديث موقع فوري
      if (driver.currentLocation != null) {
        await _notificationService.notifyCompanyOfDriverLocationUpdate(
          companyId: order.companyId,
          orderId: orderId,
          driverName: driver.name,
          driverId: driverId,
          latitude: driver.currentLocation!.latitude,
          longitude: driver.currentLocation!.longitude,
          locationName: locationName,
        );
      }

      return true;
    } catch (e) {
      _setError('خطأ في قبول الطلب: $e');
      return false;
    }
  }

  /// الحصول على اسم الموقع من الإحداثيات
  Future<String?> _getLocationName(double latitude, double longitude) async {
    try {
      // هنا يمكن استخدام خدمة Geocoding الحقيقية
      // مثل Google Geocoding API أو خدمة مجانية

      // للآن، سنستخدم أسماء مواقع تقريبية لبغداد
      if (latitude >= 33.2 && latitude <= 33.4 && longitude >= 44.2 && longitude <= 44.5) {
        final locations = [
          'منطقة الكرادة',
          'منطقة الجادرية',
          'منطقة المنصور',
          'منطقة الكاظمية',
          'منطقة الأعظمية',
          'منطقة الدورة',
          'منطقة الشعلة',
          'منطقة حي الجامعة',
          'منطقة الزعفرانية',
          'منطقة المدائن',
        ];

        // اختيار موقع عشوائي بناءً على الإحداثيات
        final index = ((latitude + longitude) * 1000).toInt() % locations.length;
        return locations[index];
      }

      return 'بغداد - موقع غير محدد';
    } catch (e) {
      return null;
    }
  }

  /// تحديث حالة الطلب
  Future<bool> updateOrderStatus(String orderId, OrderStatus status, {String? note}) async {
    try {
      await _dataService.updateOrderStatus(orderId, status, note: note);
      
      // تحديث الطلب محلياً
      final orderIndex = _orders.indexWhere((o) => o.id == orderId);
      if (orderIndex != -1) {
        _orders[orderIndex] = _orders[orderIndex].copyWith(
          status: status,
          updatedAt: DateTime.now(),
        );
        notifyListeners();
      }
      
      return true;
    } catch (e) {
      _setError('خطأ في تحديث حالة الطلب: $e');
      return false;
    }
  }

  /// إلغاء الطلب
  Future<bool> cancelOrder(String orderId, String reason) async {
    try {
      await updateOrderStatus(orderId, OrderStatus.cancelled, note: reason);
      return true;
    } catch (e) {
      _setError('خطأ في إلغاء الطلب: $e');
      return false;
    }
  }

  /// البحث في الطلبات
  void searchOrders(String query) {
    _searchQuery = query.toLowerCase();
    notifyListeners();
  }

  /// تطبيق فلتر الحالة
  void filterByStatus(OrderStatus? status) {
    _statusFilter = status;
    notifyListeners();
  }

  /// تطبيق فلتر الأولوية
  void filterByPriority(OrderPriority? priority) {
    _priorityFilter = priority;
    notifyListeners();
  }

  /// تطبيق فلتر نوع المركبة
  void filterByVehicleType(String? vehicleType) {
    _vehicleTypeFilter = vehicleType;
    notifyListeners();
  }

  /// مسح جميع الفلاتر
  void clearFilters() {
    _searchQuery = '';
    _statusFilter = null;
    _priorityFilter = null;
    _vehicleTypeFilter = null;
    notifyListeners();
  }

  /// الحصول على الطلبات المفلترة
  List<Order> _getFilteredOrders() {
    var filteredOrders = List<Order>.from(_orders);

    // تطبيق البحث النصي
    if (_searchQuery.isNotEmpty) {
      filteredOrders = filteredOrders.where((order) {
        return order.cargoType.toLowerCase().contains(_searchQuery) ||
               order.pickupLocation.address.toLowerCase().contains(_searchQuery) ||
               order.deliveryLocation.address.toLowerCase().contains(_searchQuery) ||
               order.id.toLowerCase().contains(_searchQuery);
      }).toList();
    }

    // تطبيق فلتر الحالة
    if (_statusFilter != null) {
      filteredOrders = filteredOrders.where((order) => order.status == _statusFilter).toList();
    }

    // تطبيق فلتر الأولوية
    if (_priorityFilter != null) {
      filteredOrders = filteredOrders.where((order) => order.priority == _priorityFilter).toList();
    }

    // تطبيق فلتر نوع المركبة
    if (_vehicleTypeFilter != null) {
      filteredOrders = filteredOrders.where((order) => order.vehicleType == _vehicleTypeFilter).toList();
    }

    // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
    filteredOrders.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return filteredOrders;
  }

  /// الحصول على إحصائيات الطلبات
  Map<String, int> getOrderStatistics() {
    final stats = <String, int>{};
    
    for (final status in OrderStatus.values) {
      stats[status.name] = _orders.where((order) => order.status == status).length;
    }
    
    return stats;
  }

  /// الحصول على الطلبات حسب الحالة
  List<Order> getOrdersByStatus(OrderStatus status) {
    return _orders.where((order) => order.status == status).toList();
  }

  /// الحصول على الطلبات النشطة
  List<Order> get activeOrders {
    return _orders.where((order) => [
      OrderStatus.pending,
      OrderStatus.accepted,
      OrderStatus.pickedUp,
      OrderStatus.inTransit,
    ].contains(order.status)).toList();
  }

  /// الحصول على الطلبات المكتملة
  List<Order> get completedOrders {
    return _orders.where((order) => [
      OrderStatus.delivered,
      OrderStatus.completed,
    ].contains(order.status)).toList();
  }

  /// إشعار السائقين المتاحين بالطلب الجديد
  Future<void> _notifyAvailableDrivers(Order order) async {
    try {
      // الحصول على السائقين المتاحين
      final availableDrivers = await _dataService.getAvailableDrivers(
        nearLocation: Position(
          latitude: order.pickupLocation.latitude,
          longitude: order.pickupLocation.longitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        ),
        vehicleType: order.vehicleType,
      );

      // إرسال إشعارات للسائقين
      for (final driver in availableDrivers) {
        await _notificationService.notifyDriverOfNewOrder(
          driver.id,
          order.id,
          '${order.cargoType} من ${order.pickupLocation.address} إلى ${order.deliveryLocation.address}',
        );
      }
    } catch (e) {
      debugPrint('خطأ في إشعار السائقين: $e');
    }
  }

  /// تعيين حالة الخطأ
  void _setError(String message) {
    _errorMessage = message;
    _setState(OrderState.error);
  }

  /// تعيين الحالة
  void _setState(OrderState newState) {
    _state = newState;
    if (newState != OrderState.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _errorMessage = null;
    if (_state == OrderState.error) {
      _setState(OrderState.loaded);
    }
  }

  /// تحديث الطلبات
  Future<void> refreshOrders() async {
    if (_state == OrderState.loading) return;
    
    // إعادة تحميل الطلبات حسب نوع المستخدم
    // هذا يتطلب معرفة المستخدم الحالي
    notifyListeners();
  }
}
