import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'supabase_service.dart';
import '../../core/config/supabase_config.dart';

/// خدمة إدارة الصور
class ImageService {
  static final ImageService _instance = ImageService._internal();
  factory ImageService() => _instance;
  ImageService._internal();

  final SupabaseService _supabaseService = SupabaseService();
  final ImagePicker _picker = ImagePicker();

  // ═══════════════════════════════════════════════════════════════
  // اختيار الصور
  // ═══════════════════════════════════════════════════════════════

  /// اختيار صورة من المعرض
  Future<File?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      
      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      throw Exception('خطأ في اختيار الصورة من المعرض: $e');
    }
  }

  /// التقاط صورة بالكاميرا
  Future<File?> takePhoto() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      
      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      throw Exception('خطأ في التقاط الصورة: $e');
    }
  }

  /// اختيار عدة صور
  Future<List<File>> pickMultipleImages({int maxImages = 5}) async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      
      if (images.length > maxImages) {
        throw Exception('يمكنك اختيار $maxImages صور كحد أقصى');
      }
      
      return images.map((image) => File(image.path)).toList();
    } catch (e) {
      throw Exception('خطأ في اختيار الصور: $e');
    }
  }

  /// عرض خيارات اختيار الصورة
  Future<File?> showImagePickerOptions(BuildContext context) async {
    return await showModalBottomSheet<File?>(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('اختيار من المعرض'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final file = await pickImageFromGallery();
                  Navigator.of(context).pop(file);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('التقاط صورة'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final file = await takePhoto();
                  Navigator.of(context).pop(file);
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text('إلغاء'),
                onTap: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        );
      },
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // رفع الصور
  // ═══════════════════════════════════════════════════════════════

  /// رفع صورة شخصية
  Future<String> uploadProfileImage(File imageFile, {String? customName, String? userId}) async {
    try {
      final fileName = customName ?? 'avatar_${DateTime.now().millisecondsSinceEpoch}.jpg';
      return await _supabaseService.uploadProfileImage(fileName, imageFile, userId: userId);
    } catch (e) {
      throw Exception('خطأ في رفع الصورة الشخصية: $e');
    }
  }

  /// رفع صورة مركبة
  Future<String> uploadVehicleImage(File imageFile, String imageType, {String? userId}) async {
    try {
      final fileName = '${imageType}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      return await _supabaseService.uploadVehicleImage(fileName, imageFile, userId: userId);
    } catch (e) {
      throw Exception('خطأ في رفع صورة المركبة: $e');
    }
  }

  /// رفع صورة طلب
  Future<String> uploadOrderImage(String orderId, File imageFile, String imageType, {String? userId}) async {
    try {
      final fileName = '${imageType}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      return await _supabaseService.uploadOrderImage(orderId, fileName, imageFile, userId: userId);
    } catch (e) {
      throw Exception('خطأ في رفع صورة الطلب: $e');
    }
  }

  /// رفع عدة صور للطلب
  Future<List<String>> uploadOrderImages(String orderId, List<File> imageFiles, String imageType, {String? userId}) async {
    try {
      final List<String> urls = [];

      for (int i = 0; i < imageFiles.length; i++) {
        final fileName = '${imageType}_${i + 1}_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final url = await _supabaseService.uploadOrderImage(orderId, fileName, imageFiles[i], userId: userId);
        urls.add(url);
      }

      return urls;
    } catch (e) {
      throw Exception('خطأ في رفع صور الطلب: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // حذف الصور
  // ═══════════════════════════════════════════════════════════════

  /// حذف صورة شخصية
  Future<void> deleteProfileImage(String fileName, {String? userId}) async {
    try {
      await _supabaseService.deleteProfileImage(fileName, userId: userId);
    } catch (e) {
      throw Exception('خطأ في حذف الصورة الشخصية: $e');
    }
  }

  /// حذف صورة مركبة
  Future<void> deleteVehicleImage(String fileName, {String? userId}) async {
    try {
      await _supabaseService.deleteVehicleImage(fileName, userId: userId);
    } catch (e) {
      throw Exception('خطأ في حذف صورة المركبة: $e');
    }
  }

  /// حذف صورة طلب
  Future<void> deleteOrderImage(String orderId, String fileName, {String? userId}) async {
    try {
      await _supabaseService.deleteOrderImage(orderId, fileName, userId: userId);
    } catch (e) {
      throw Exception('خطأ في حذف صورة الطلب: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال مساعدة
  // ═══════════════════════════════════════════════════════════════

  /// التحقق من صحة الصورة
  bool isValidImageFile(File file) {
    final extension = path.extension(file.path).toLowerCase();
    final validExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
    return validExtensions.contains(extension);
  }

  /// التحقق من حجم الصورة
  Future<bool> isValidImageSize(File file, {int maxSizeInMB = 5}) async {
    final fileSize = await file.length();
    final maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return fileSize <= maxSizeInBytes;
  }

  /// ضغط الصورة
  Future<File> compressImage(File file) async {
    // يمكن إضافة مكتبة ضغط الصور هنا مثل flutter_image_compress
    // حالياً نعيد الملف كما هو
    return file;
  }

  /// الحصول على اسم الملف من الرابط
  String getFileNameFromUrl(String url) {
    final uri = Uri.parse(url);
    return path.basename(uri.path);
  }

  /// الحصول على رابط آمن لصورة طلب
  Future<String> getOrderImageSignedUrl(String orderId, String fileName, {String? userId}) async {
    try {
      return await _supabaseService.getOrderImageSignedUrl(orderId, fileName, userId: userId);
    } catch (e) {
      throw Exception('خطأ في الحصول على رابط الصورة: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // أنواع الصور المختلفة
  // ═══════════════════════════════════════════════════════════════

  /// أنواع صور المركبات
  static const Map<String, String> vehicleImageTypes = {
    'front': 'مقدمة المركبة',
    'back': 'مؤخرة المركبة',
    'side': 'جانب المركبة',
    'license': 'رخصة القيادة',
    'registration': 'استمارة المركبة',
    'insurance': 'تأمين المركبة',
  };

  /// أنواع صور الطلبات
  static const Map<String, String> orderImageTypes = {
    'cargo_before': 'البضاعة قبل التحميل',
    'cargo_after': 'البضاعة بعد التحميل',
    'delivery_proof': 'إثبات التسليم',
    'damage_report': 'تقرير الأضرار',
    'receipt': 'الإيصال',
    'signature': 'التوقيع',
  };

  /// الحصول على أيقونة نوع الصورة
  IconData getImageTypeIcon(String imageType) {
    switch (imageType) {
      case 'front':
      case 'back':
      case 'side':
        return Icons.directions_car;
      case 'license':
        return Icons.credit_card;
      case 'registration':
      case 'insurance':
        return Icons.description;
      case 'cargo_before':
      case 'cargo_after':
        return Icons.inventory;
      case 'delivery_proof':
        return Icons.check_circle;
      case 'damage_report':
        return Icons.report_problem;
      case 'receipt':
        return Icons.receipt;
      case 'signature':
        return Icons.draw;
      default:
        return Icons.image;
    }
  }
}
