import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:icons_plus/icons_plus.dart';
import 'dart:async';
import 'dart:math' as math;

import '../../core/constants/app_colors.dart';
import '../../core/utils/language_manager.dart';
import '../../core/services/driver_location_service.dart';
import '../../shared/widgets/driver_map_marker.dart';
import '../../shared/services/speed_monitoring_service.dart';
import '../../shared/services/notification_service.dart';

class LiveTrackingMapScreen extends StatefulWidget {
  final List<Map<String, dynamic>> shipments;

  const LiveTrackingMapScreen({
    super.key,
    required this.shipments,
  });

  @override
  State<LiveTrackingMapScreen> createState() => _LiveTrackingMapScreenState();
}

class _LiveTrackingMapScreenState extends State<LiveTrackingMapScreen>
    with TickerProviderStateMixin {
  
  // ═══════════════════════════════════════════════════════════════
  // المتغيرات والحالات
  // ═══════════════════════════════════════════════════════════════
  
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  late Timer _updateTimer;
  
  double _zoomLevel = 1.0;
  bool _showSpeedInfo = true;
  bool _showDriverNames = true;
  String _selectedFilter = 'all'; // all, speeding, normal
  
  final SpeedMonitoringService _speedService = SpeedMonitoringService();
  final NotificationService _notificationService = NotificationService();
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startRealTimeUpdates();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _updateTimer.cancel();
    super.dispose();
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال التهيئة والرسوم المتحركة
  // ═══════════════════════════════════════════════════════════════
  
  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _pulseController.repeat(reverse: true);
  }
  
  void _startRealTimeUpdates() {
    _updateTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted) {
        setState(() {
          // محاكاة تحديث مواقع السائقين
          _updateDriverLocations();
        });
      }
    });
  }
  
  void _updateDriverLocations() {
    // محاكاة تحديث المواقع والسرعات
    for (var shipment in widget.shipments) {
      // تحديث السرعة بشكل عشوائي
      final random = math.Random();
      final currentSpeed = shipment['currentSpeed'] as int;
      final newSpeed = math.max(40, math.min(120, currentSpeed + random.nextInt(21) - 10));
      shipment['currentSpeed'] = newSpeed;
      
      // تحديث الموقع قليلاً
      final lat = shipment['driverLatitude'] as double;
      final lng = shipment['driverLongitude'] as double;
      shipment['driverLatitude'] = lat + (random.nextDouble() - 0.5) * 0.01;
      shipment['driverLongitude'] = lng + (random.nextDouble() - 0.5) * 0.01;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Scaffold(
          backgroundColor: const Color(0xFF0F172A),
          appBar: _buildAppBar(langManager),
          body: Stack(
            children: [
              _buildMapContainer(langManager),
              _buildControlsOverlay(langManager),
              _buildBottomSheet(langManager),
            ],
          ),
        );
      },
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // بناء شريط التطبيق
  // ═══════════════════════════════════════════════════════════════
  
  PreferredSizeWidget _buildAppBar(LanguageManager langManager) {
    return AppBar(
      backgroundColor: const Color(0xFF1E293B),
      elevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(Bootstrap.arrow_left, color: Colors.white),
      ),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Bootstrap.map,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  langManager.getText('خريطة التتبع المباشر', 'Live Tracking Map'),
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  langManager.getText('${widget.shipments.length} شحنة نشطة', '${widget.shipments.length} active shipments'),
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          onPressed: () => _showMapSettings(langManager),
          icon: const Icon(Bootstrap.gear, color: Colors.white),
        ),
      ],
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // بناء حاوية الخريطة
  // ═══════════════════════════════════════════════════════════════
  
  Widget _buildMapContainer(LanguageManager langManager) {
    final filteredShipments = _getFilteredShipments();
    
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1E293B),
            const Color(0xFF334155),
            const Color(0xFF475569),
          ],
        ),
      ),
      child: Stack(
        children: [
          // خلفية الخريطة
          _buildMapBackground(),
          
          // عرض السائقين على الخريطة
          ...filteredShipments.map((shipment) => _buildDriverMarker(shipment, langManager)),
          
          // خطوط الطرق (اختيارية)
          _buildRoadLines(),
        ],
      ),
    );
  }
  
  Widget _buildMapBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.center,
          radius: 1.5,
          colors: [
            const Color(0xFF2D3748),
            const Color(0xFF1A202C),
            const Color(0xFF0F172A),
          ],
        ),
      ),
      child: CustomPaint(
        size: Size.infinite,
        painter: MapGridPainter(),
      ),
    );
  }
  
  Widget _buildRoadLines() {
    return CustomPaint(
      size: Size.infinite,
      painter: RoadLinesPainter(),
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // بناء علامات السائقين على الخريطة
  // ═══════════════════════════════════════════════════════════════
  
  Widget _buildDriverMarker(Map<String, dynamic> shipment, LanguageManager langManager) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    
    // تحويل الإحداثيات الجغرافية إلى إحداثيات الشاشة
    final x = ((shipment['driverLongitude'] - 43.0) * 50 + screenWidth * 0.3) * _zoomLevel;
    final y = ((36.0 - shipment['driverLatitude']) * 50 + screenHeight * 0.2) * _zoomLevel;
    
    final isOverSpeeding = shipment['currentSpeed'] > shipment['speedLimit'];
    
    return Positioned(
      left: x,
      top: y,
      child: GestureDetector(
        onTap: () => _showDriverDetails(shipment, langManager),
        child: AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: isOverSpeeding ? _pulseAnimation.value : 1.0,
              child: DriverMapMarker(
                shipment: shipment,
                showSpeedInfo: _showSpeedInfo,
                showDriverName: _showDriverNames,
                isOverSpeeding: isOverSpeeding,
              ),
            );
          },
        ),
      ),
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال المساعدة والفلترة
  // ═══════════════════════════════════════════════════════════════
  
  List<Map<String, dynamic>> _getFilteredShipments() {
    switch (_selectedFilter) {
      case 'speeding':
        return widget.shipments.where((s) => s['currentSpeed'] > s['speedLimit']).toList();
      case 'normal':
        return widget.shipments.where((s) => s['currentSpeed'] <= s['speedLimit']).toList();
      default:
        return widget.shipments;
    }
  }
  
  Color _getSpeedColor(int currentSpeed, int speedLimit) {
    if (currentSpeed > speedLimit) {
      return AppColors.error;
    } else if (currentSpeed > speedLimit * 0.9) {
      return Colors.orange;
    } else {
      return AppColors.success;
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // بناء عناصر التحكم
  // ═══════════════════════════════════════════════════════════════

  Widget _buildControlsOverlay(LanguageManager langManager) {
    return Positioned(
      top: 20,
      right: 20,
      child: Column(
        children: [
          // أزرار التكبير والتصغير
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                IconButton(
                  onPressed: _zoomIn,
                  icon: const Icon(Bootstrap.plus, color: Colors.white),
                ),
                Container(
                  width: 30,
                  height: 1,
                  color: Colors.white30,
                ),
                IconButton(
                  onPressed: _zoomOut,
                  icon: const Icon(Bootstrap.dash, color: Colors.white),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // فلاتر العرض
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                _buildFilterButton(
                  icon: Bootstrap.eye,
                  isActive: _selectedFilter == 'all',
                  onTap: () => setState(() => _selectedFilter = 'all'),
                  tooltip: langManager.getText('عرض الجميع', 'Show All'),
                ),
                const SizedBox(height: 8),
                _buildFilterButton(
                  icon: Bootstrap.exclamation_triangle,
                  isActive: _selectedFilter == 'speeding',
                  onTap: () => setState(() => _selectedFilter = 'speeding'),
                  tooltip: langManager.getText('المتجاوزين للسرعة', 'Speeding'),
                ),
                const SizedBox(height: 8),
                _buildFilterButton(
                  icon: Bootstrap.check_circle,
                  isActive: _selectedFilter == 'normal',
                  onTap: () => setState(() => _selectedFilter = 'normal'),
                  tooltip: langManager.getText('السرعة العادية', 'Normal Speed'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onTap,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: isActive ? AppColors.primary : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isActive ? AppColors.primary : Colors.white30,
              width: 1,
            ),
          ),
          child: Icon(
            icon,
            color: isActive ? Colors.white : Colors.white70,
            size: 20,
          ),
        ),
      ),
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // بناء الورقة السفلية
  // ═══════════════════════════════════════════════════════════════

  Widget _buildBottomSheet(LanguageManager langManager) {
    final speedingDrivers = widget.shipments.where((s) => s['currentSpeed'] > s['speedLimit']).length;

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        height: 120,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF1E293B),
              const Color(0xFF0F172A),
            ],
          ),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              // إحصائيات سريعة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      langManager.getText('إحصائيات سريعة', 'Quick Stats'),
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        _buildStatItem(
                          icon: Bootstrap.truck,
                          value: '${widget.shipments.length}',
                          label: langManager.getText('شحنة نشطة', 'Active'),
                          color: AppColors.info,
                        ),
                        const SizedBox(width: 16),
                        _buildStatItem(
                          icon: Bootstrap.exclamation_triangle,
                          value: '$speedingDrivers',
                          label: langManager.getText('متجاوز للسرعة', 'Speeding'),
                          color: AppColors.error,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // زر الإعدادات
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: AppColors.primary.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: () => _showMapSettings(langManager),
                  icon: const Icon(
                    Bootstrap.gear,
                    color: AppColors.primary,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(icon, color: color, size: 14),
        ),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: GoogleFonts.tajawal(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            Text(
              label,
              style: GoogleFonts.tajawal(
                fontSize: 10,
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال التحكم والتفاعل
  // ═══════════════════════════════════════════════════════════════

  void _zoomIn() {
    setState(() {
      _zoomLevel = math.min(_zoomLevel * 1.2, 3.0);
    });
  }

  void _zoomOut() {
    setState(() {
      _zoomLevel = math.max(_zoomLevel / 1.2, 0.5);
    });
  }

  void _showMapSettings(LanguageManager langManager) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1E293B),
              Color(0xFF0F172A),
            ],
          ),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                langManager.getText('إعدادات الخريطة', 'Map Settings'),
                style: GoogleFonts.tajawal(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 24),

              SwitchListTile(
                title: Text(
                  langManager.getText('عرض معلومات السرعة', 'Show Speed Info'),
                  style: GoogleFonts.tajawal(color: Colors.white),
                ),
                value: _showSpeedInfo,
                onChanged: (value) => setState(() => _showSpeedInfo = value),
                activeColor: AppColors.primary,
              ),

              SwitchListTile(
                title: Text(
                  langManager.getText('عرض أسماء السائقين', 'Show Driver Names'),
                  style: GoogleFonts.tajawal(color: Colors.white),
                ),
                value: _showDriverNames,
                onChanged: (value) => setState(() => _showDriverNames = value),
                activeColor: AppColors.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDriverDetails(Map<String, dynamic> shipment, LanguageManager langManager) {
    final isOverSpeeding = shipment['currentSpeed'] > shipment['speedLimit'];

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1E293B),
              Color(0xFF0F172A),
            ],
          ),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان
              Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: _getSpeedColor(shipment['currentSpeed'], shipment['speedLimit']).withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Bootstrap.truck,
                      color: _getSpeedColor(shipment['currentSpeed'], shipment['speedLimit']),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          shipment['driver'],
                          style: GoogleFonts.tajawal(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          shipment['vehicle'],
                          style: GoogleFonts.tajawal(
                            fontSize: 14,
                            color: Colors.white70,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isOverSpeeding)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: AppColors.error,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Bootstrap.exclamation_triangle, color: Colors.white, size: 16),
                          const SizedBox(width: 4),
                          Text(
                            langManager.getText('تجاوز السرعة', 'Speeding'),
                            style: GoogleFonts.tajawal(
                              fontSize: 12,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 24),

              // معلومات السرعة
              _buildSpeedInfoContainer(shipment, langManager),

              const SizedBox(height: 16),

              // معلومات الرحلة
              _buildTripInfoContainer(shipment, langManager),

              const Spacer(),

              // أزرار الإجراءات
              Column(
                children: [
                  // زر الموقع الحقيقي
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        _openDriverRealLocation(
                          shipment['driverId'] ?? 'driver_${shipment['id']}',
                          shipment['driver'],
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.success,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      icon: const Icon(Bootstrap.geo_alt_fill, color: Colors.white),
                      label: Text(
                        langManager.getText('الموقع الحقيقي', 'Real Location'),
                        style: GoogleFonts.tajawal(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),

                  // زر تنبيه السرعة (إذا كان يتجاوز السرعة)
                  if (isOverSpeeding) ...[
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          _sendSpeedAlert(shipment, langManager);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.error,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        icon: const Icon(Bootstrap.bell, color: Colors.white),
                        label: Text(
                          langManager.getText('إرسال تنبيه السرعة', 'Send Speed Alert'),
                          style: GoogleFonts.tajawal(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSpeedInfoContainer(Map<String, dynamic> shipment, LanguageManager langManager) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Bootstrap.speedometer, color: AppColors.info, size: 20),
              const SizedBox(width: 12),
              Text(
                langManager.getText('معلومات السرعة', 'Speed Information'),
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSpeedInfo(
                  label: langManager.getText('السرعة الحالية', 'Current Speed'),
                  value: '${shipment['currentSpeed']} كم/س',
                  color: _getSpeedColor(shipment['currentSpeed'], shipment['speedLimit']),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSpeedInfo(
                  label: langManager.getText('الحد الأقصى', 'Speed Limit'),
                  value: '${shipment['speedLimit']} كم/س',
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTripInfoContainer(Map<String, dynamic> shipment, LanguageManager langManager) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Bootstrap.geo_alt, color: AppColors.info, size: 20),
              const SizedBox(width: 12),
              Text(
                langManager.getText('معلومات الرحلة', 'Trip Information'),
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            label: langManager.getText('من', 'From'),
            value: shipment['from'],
          ),
          const SizedBox(height: 8),
          _buildInfoRow(
            label: langManager.getText('إلى', 'To'),
            value: shipment['to'],
          ),
          const SizedBox(height: 8),
          _buildInfoRow(
            label: langManager.getText('الموقع الحالي', 'Current Location'),
            value: shipment['currentLocation'],
          ),
        ],
      ),
    );
  }

  Widget _buildSpeedInfo({
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.tajawal(
            fontSize: 12,
            color: Colors.white70,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.tajawal(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow({required String label, required String value}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: GoogleFonts.tajawal(
              fontSize: 12,
              color: Colors.white70,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: GoogleFonts.tajawal(
              fontSize: 12,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// فتح موقع السائق الحقيقي في الخرائط الخارجية
  Future<void> _openDriverRealLocation(String driverId, String driverName) async {
    await DriverLocationService.openDriverRealLocation(
      context: context,
      driverId: driverId,
      driverName: driverName,
    );
  }

  void _sendSpeedAlert(Map<String, dynamic> shipment, LanguageManager langManager) {
    _speedService.sendSpeedAlert(
      driverId: shipment['id'],
      driverName: shipment['driver'],
      currentSpeed: shipment['currentSpeed'],
      speedLimit: shipment['speedLimit'],
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          langManager.getText(
            'تم إرسال تنبيه السرعة إلى ${shipment['driver']}',
            'Speed alert sent to ${shipment['driver']}',
          ),
          style: GoogleFonts.tajawal(color: Colors.white),
        ),
        backgroundColor: AppColors.warning,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}

/// رسام شبكة الخريطة
class MapGridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    final gridSize = 50.0;

    // رسم الخطوط العمودية
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // رسم الخطوط الأفقية
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
