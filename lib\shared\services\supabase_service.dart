import 'dart:async';
import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart' hide User;
import 'package:supabase_flutter/supabase_flutter.dart' as supabase show User;
import 'package:geolocator/geolocator.dart';
import '../models/index.dart';
import '../../core/config/supabase_config.dart';
import 'storage_error_handler.dart';

typedef SupabaseUser = supabase.User;

/// خدمة Supabase للتعامل مع قاعدة البيانات
class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  /// الحصول على عميل Supabase
  SupabaseClient get client => Supabase.instance.client;

  /// تهيئة Supabase
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: SupabaseConfig.supabaseUrl,
      anonKey: SupabaseConfig.supabaseAnonKey,
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // خدمات المصادقة
  // ═══════════════════════════════════════════════════════════════

  /// تسجيل الدخول
  Future<SupabaseUser?> signIn(String email, String password) async {
    try {
      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response.user;
    } catch (e) {
      throw Exception('خطأ في تسجيل الدخول: $e');
    }
  }

  /// تسجيل حساب جديد
  Future<SupabaseUser?> signUp(String email, String password, Map<String, dynamic> userData) async {
    try {
      print('🔐 محاولة تسجيل حساب جديد في Supabase Auth: $email');
      final response = await client.auth.signUp(
        email: email,
        password: password,
        data: userData,
      );
      print('✅ تم تسجيل الحساب في Supabase Auth بنجاح: ${response.user?.id}');
      return response.user;
    } catch (e) {
      print('❌ خطأ في تسجيل الحساب في Supabase Auth: $e');
      throw Exception('خطأ في إنشاء الحساب: $e');
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      await client.auth.signOut();
    } catch (e) {
      throw Exception('خطأ في تسجيل الخروج: $e');
    }
  }

  /// الحصول على المستخدم الحالي
  SupabaseUser? get currentUser => client.auth.currentUser;

  /// الاستماع لتغييرات حالة المصادقة
  Stream<AuthState> get authStateChanges => client.auth.onAuthStateChange;

  // ═══════════════════════════════════════════════════════════════
  // خدمات المستخدمين
  // ═══════════════════════════════════════════════════════════════

  /// إنشاء مستخدم جديد
  Future<String> createUser(Map<String, dynamic> userData) async {
    try {
      print('📝 إنشاء مستخدم في جدول users: ${userData['email']}');
      final response = await client
          .from(SupabaseConfig.usersTable)
          .insert(userData)
          .select()
          .single();
      print('✅ تم إنشاء المستخدم في جدول users بنجاح: ${response['id']}');
      return response['id'] as String;
    } catch (e) {
      print('❌ خطأ في إنشاء المستخدم في جدول users: $e');
      throw Exception('خطأ في إنشاء المستخدم: $e');
    }
  }

  /// الحصول على مستخدم بالمعرف
  Future<Map<String, dynamic>?> getUserById(String userId) async {
    try {
      final response = await client
          .from(SupabaseConfig.usersTable)
          .select()
          .eq('id', userId)
          .maybeSingle();
      return response;
    } catch (e) {
      throw Exception('خطأ في جلب المستخدم: $e');
    }
  }

  /// تحديث بيانات المستخدم
  Future<void> updateUser(String userId, Map<String, dynamic> updates) async {
    try {
      updates['updated_at'] = DateTime.now().toIso8601String();
      await client
          .from(SupabaseConfig.usersTable)
          .update(updates)
          .eq('id', userId);
    } catch (e) {
      throw Exception('خطأ في تحديث المستخدم: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // خدمات السائقين
  // ═══════════════════════════════════════════════════════════════

  /// إنشاء سائق جديد
  Future<String> createDriver(Map<String, dynamic> driverData) async {
    try {
      print('📝 إنشاء سائق في جدول drivers: ${driverData['user_id']}');
      final response = await client
          .from(SupabaseConfig.driversTable)
          .insert(driverData)
          .select()
          .single();
      print('✅ تم إنشاء السائق في جدول drivers بنجاح: ${response['id']}');
      return response['id'] as String;
    } catch (e) {
      print('❌ خطأ في إنشاء السائق في جدول drivers: $e');
      throw Exception('خطأ في إنشاء السائق: $e');
    }
  }

  /// الحصول على السائقين المتوفرين
  Future<List<Map<String, dynamic>>> getAvailableDrivers({
    Position? nearLocation,
    double maxDistance = 50.0,
    String? vehicleType,
  }) async {
    try {
      var query = client
          .from(SupabaseConfig.driversTable)
          .select('''
            *,
            users!inner(*)
          ''')
          .eq('driver_status', 'available')
          .eq('users.status', 'active')
          .eq('users.verification_status', 'verified');

      if (vehicleType != null) {
        query = query.eq('vehicle_type', vehicleType);
      }

      final response = await query;
      
      // تطبيق فلتر المسافة إذا تم تحديد موقع
      if (nearLocation != null) {
        final filteredDrivers = <Map<String, dynamic>>[];
        for (final driver in response) {
          if (driver['current_location'] != null) {
            // حساب المسافة (يحتاج تحسين للاستعلام المباشر من قاعدة البيانات)
            filteredDrivers.add(driver);
          }
        }
        return filteredDrivers;
      }

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('خطأ في جلب السائقين المتوفرين: $e');
    }
  }

  /// تحديث موقع السائق
  Future<void> updateDriverLocation(String driverId, Position position) async {
    try {
      await client
          .from(SupabaseConfig.driversTable)
          .update({
            'current_location': 'POINT(${position.longitude} ${position.latitude})',
            'last_location_update': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', driverId);
    } catch (e) {
      throw Exception('خطأ في تحديث موقع السائق: $e');
    }
  }

  /// تحديث حالة السائق
  Future<void> updateDriverStatus(String driverId, String status) async {
    try {
      await client
          .from(SupabaseConfig.driversTable)
          .update({
            'driver_status': status,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', driverId);
    } catch (e) {
      throw Exception('خطأ في تحديث حالة السائق: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // خدمات الشركات
  // ═══════════════════════════════════════════════════════════════

  /// إنشاء شركة جديدة
  Future<String> createCompany(Map<String, dynamic> companyData) async {
    try {
      print('📝 إنشاء شركة في جدول companies: ${companyData['user_id']}');
      final response = await client
          .from(SupabaseConfig.companiesTable)
          .insert(companyData)
          .select()
          .single();
      print('✅ تم إنشاء الشركة في جدول companies بنجاح: ${response['id']}');
      return response['id'] as String;
    } catch (e) {
      print('❌ خطأ في إنشاء الشركة في جدول companies: $e');
      throw Exception('خطأ في إنشاء الشركة: $e');
    }
  }

  /// الحصول على سائق بمعرف المستخدم
  Future<Map<String, dynamic>?> getDriverByUserId(String userId) async {
    try {
      final response = await client
          .from(SupabaseConfig.driversTable)
          .select()
          .eq('user_id', userId)
          .maybeSingle();
      return response;
    } catch (e) {
      throw Exception('خطأ في جلب السائق: $e');
    }
  }

  /// الحصول على شركة بمعرف المستخدم
  Future<Map<String, dynamic>?> getCompanyByUserId(String userId) async {
    try {
      final response = await client
          .from(SupabaseConfig.companiesTable)
          .select()
          .eq('user_id', userId)
          .maybeSingle();
      return response;
    } catch (e) {
      throw Exception('خطأ في جلب الشركة: $e');
    }
  }

  /// الحصول على شركة بالمعرف
  Future<Map<String, dynamic>?> getCompanyById(String companyId) async {
    try {
      final response = await client
          .from(SupabaseConfig.companiesTable)
          .select('''
            *,
            users!inner(*)
          ''')
          .eq('id', companyId)
          .maybeSingle();
      return response;
    } catch (e) {
      throw Exception('خطأ في جلب الشركة: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // خدمات الطلبات
  // ═══════════════════════════════════════════════════════════════

  /// إنشاء طلب جديد
  Future<String> createOrder(Map<String, dynamic> orderData) async {
    try {
      final response = await client
          .from(SupabaseConfig.ordersTable)
          .insert(orderData)
          .select()
          .single();
      
      // إضافة سجل في تاريخ الحالات
      await _addOrderStatusHistory(response['id'], 'pending', 'تم إنشاء الطلب');
      
      return response['id'] as String;
    } catch (e) {
      throw Exception('خطأ في إنشاء الطلب: $e');
    }
  }

  /// الحصول على طلبات الشركة
  Future<List<Map<String, dynamic>>> getCompanyOrders(String companyId) async {
    try {
      final response = await client
          .from(SupabaseConfig.ordersTable)
          .select('''
            *,
            drivers(*, users(*))
          ''')
          .eq('company_id', companyId)
          .order('created_at', ascending: false);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('خطأ في جلب طلبات الشركة: $e');
    }
  }

  /// الحصول على طلبات السائق
  Future<List<Map<String, dynamic>>> getDriverOrders(String driverId) async {
    try {
      final response = await client
          .from(SupabaseConfig.ordersTable)
          .select('''
            *,
            companies(*, users(*))
          ''')
          .eq('driver_id', driverId)
          .order('created_at', ascending: false);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('خطأ في جلب طلبات السائق: $e');
    }
  }

  /// الحصول على الطلبات المتاحة للسائق
  Future<List<Map<String, dynamic>>> getAvailableOrdersForDriver(String driverId) async {
    try {
      // الحصول على نوع مركبة السائق أولاً
      final driverResponse = await client
          .from(SupabaseConfig.driversTable)
          .select('vehicle_type')
          .eq('id', driverId)
          .single();
      
      final vehicleType = driverResponse['vehicle_type'] as String;
      
      final response = await client
          .from(SupabaseConfig.ordersTable)
          .select('''
            *,
            companies(*, users(*))
          ''')
          .eq('status', 'pending')
          .eq('vehicle_type', vehicleType)
          .order('created_at', ascending: false);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('خطأ في جلب الطلبات المتاحة: $e');
    }
  }

  /// تحديث حالة الطلب
  Future<void> updateOrderStatus(String orderId, String status, {String? note}) async {
    try {
      await client
          .from(SupabaseConfig.ordersTable)
          .update({
            'status': status,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', orderId);
      
      // إضافة سجل في تاريخ الحالات
      await _addOrderStatusHistory(orderId, status, note);
    } catch (e) {
      throw Exception('خطأ في تحديث حالة الطلب: $e');
    }
  }

  /// تعيين سائق للطلب
  Future<void> assignDriverToOrder(String orderId, String driverId) async {
    try {
      await client
          .from(SupabaseConfig.ordersTable)
          .update({
            'driver_id': driverId,
            'status': 'accepted',
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', orderId);
      
      // تحديث حالة السائق
      await updateDriverStatus(driverId, 'busy');
      
      // إضافة سجل في تاريخ الحالات
      await _addOrderStatusHistory(orderId, 'accepted', 'تم قبول الطلب من قبل السائق');
    } catch (e) {
      throw Exception('خطأ في تعيين السائق للطلب: $e');
    }
  }

  /// إضافة سجل في تاريخ حالات الطلب
  Future<void> _addOrderStatusHistory(String orderId, String status, [String? note]) async {
    try {
      await client
          .from(SupabaseConfig.orderStatusHistoryTable)
          .insert({
            'order_id': orderId,
            'status': status,
            'note': note,
            'updated_by': currentUser?.id,
          });
    } catch (e) {
      // تسجيل الخطأ فقط دون إيقاف العملية
      print('خطأ في إضافة سجل تاريخ الحالة: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // خدمات الإشعارات
  // ═══════════════════════════════════════════════════════════════

  /// إرسال إشعار
  Future<void> sendNotification(Map<String, dynamic> notificationData) async {
    try {
      await client
          .from(SupabaseConfig.notificationsTable)
          .insert(notificationData);
    } catch (e) {
      throw Exception('خطأ في إرسال الإشعار: $e');
    }
  }

  /// الحصول على إشعارات المستخدم
  Future<List<Map<String, dynamic>>> getUserNotifications(String userId) async {
    try {
      final response = await client
          .from(SupabaseConfig.notificationsTable)
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('خطأ في جلب الإشعارات: $e');
    }
  }

  /// تحديد إشعار كمقروء
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await client
          .from(SupabaseConfig.notificationsTable)
          .update({'is_read': true})
          .eq('id', notificationId);
    } catch (e) {
      throw Exception('خطأ في تحديد الإشعار كمقروء: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // خدمات التخزين (Storage)
  // ═══════════════════════════════════════════════════════════════

  /// رفع صورة شخصية
  Future<String> uploadProfileImage(String fileName, File file) async {
    try {
      final userId = currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مسجل دخول');

      // التحقق من صحة نوع الملف
      if (!StorageErrorHandler.isValidFileType(fileName)) {
        throw Exception('نوع الملف غير مدعوم. يرجى استخدام JPG, PNG, أو WEBP');
      }

      // التحقق من حجم الملف
      final fileSize = await file.length();
      if (!StorageErrorHandler.isValidFileSize(fileSize)) {
        throw Exception('حجم الملف كبير جداً. الحد الأقصى 5MB');
      }

      final filePath = StorageErrorHandler.createUserFilePath(userId, fileName);

      await client.storage
          .from(SupabaseConfig.profileImagesBucket)
          .upload(filePath, file);

      final url = client.storage
          .from(SupabaseConfig.profileImagesBucket)
          .getPublicUrl(filePath);

      return url;
    } catch (e) {
      throw Exception(StorageErrorHandler.handleStorageError(e));
    }
  }

  /// رفع صورة مركبة
  Future<String> uploadVehicleImage(String fileName, File file) async {
    try {
      final userId = currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مسجل دخول');

      // التحقق من صحة نوع الملف
      if (!StorageErrorHandler.isValidFileType(fileName)) {
        throw Exception('نوع الملف غير مدعوم. يرجى استخدام JPG, PNG, أو WEBP');
      }

      // التحقق من حجم الملف
      final fileSize = await file.length();
      if (!StorageErrorHandler.isValidFileSize(fileSize)) {
        throw Exception('حجم الملف كبير جداً. الحد الأقصى 5MB');
      }

      final filePath = StorageErrorHandler.createUserFilePath(userId, fileName);

      await client.storage
          .from(SupabaseConfig.vehicleImagesBucket)
          .upload(filePath, file);

      final url = client.storage
          .from(SupabaseConfig.vehicleImagesBucket)
          .getPublicUrl(filePath);

      return url;
    } catch (e) {
      throw Exception(StorageErrorHandler.handleStorageError(e));
    }
  }

  /// رفع صورة طلب
  Future<String> uploadOrderImage(String orderId, String fileName, File file) async {
    try {
      final userId = currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مسجل دخول');

      // التحقق من صحة نوع الملف
      if (!StorageErrorHandler.isValidFileType(fileName)) {
        throw Exception('نوع الملف غير مدعوم. يرجى استخدام JPG, PNG, أو WEBP');
      }

      // التحقق من حجم الملف
      final fileSize = await file.length();
      if (!StorageErrorHandler.isValidFileSize(fileSize)) {
        throw Exception('حجم الملف كبير جداً. الحد الأقصى 5MB');
      }

      final filePath = StorageErrorHandler.createOrderFilePath(orderId, fileName);

      await client.storage
          .from(SupabaseConfig.orderImagesBucket)
          .upload(filePath, file);

      // للصور الخاصة، نستخدم signed URL
      final url = await client.storage
          .from(SupabaseConfig.orderImagesBucket)
          .createSignedUrl(filePath, 60 * 60 * 24 * 365); // صالح لسنة واحدة

      return url;
    } catch (e) {
      throw Exception(StorageErrorHandler.handleStorageError(e));
    }
  }

  /// حذف صورة
  Future<void> deleteImage(String bucketName, String filePath) async {
    try {
      await client.storage
          .from(bucketName)
          .remove([filePath]);
    } catch (e) {
      throw Exception(StorageErrorHandler.handleStorageError(e));
    }
  }

  /// حذف صورة شخصية
  Future<void> deleteProfileImage(String fileName) async {
    try {
      final userId = currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مسجل دخول');

      final filePath = StorageErrorHandler.createUserFilePath(userId, fileName);

      // التحقق من صحة المسار
      if (!StorageErrorHandler.isValidFilePath(filePath, userId)) {
        throw Exception('مسار الملف غير صحيح');
      }

      await deleteImage(SupabaseConfig.profileImagesBucket, filePath);
    } catch (e) {
      throw Exception(StorageErrorHandler.handleStorageError(e));
    }
  }

  /// حذف صورة مركبة
  Future<void> deleteVehicleImage(String fileName) async {
    try {
      final userId = currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مسجل دخول');

      final filePath = StorageErrorHandler.createUserFilePath(userId, fileName);

      // التحقق من صحة المسار
      if (!StorageErrorHandler.isValidFilePath(filePath, userId)) {
        throw Exception('مسار الملف غير صحيح');
      }

      await deleteImage(SupabaseConfig.vehicleImagesBucket, filePath);
    } catch (e) {
      throw Exception(StorageErrorHandler.handleStorageError(e));
    }
  }

  /// حذف صورة طلب
  Future<void> deleteOrderImage(String orderId, String fileName) async {
    try {
      final userId = currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مسجل دخول');

      final filePath = StorageErrorHandler.createOrderFilePath(orderId, fileName);

      // التحقق من صحة المسار
      if (!StorageErrorHandler.isValidOrderFilePath(filePath, orderId)) {
        throw Exception('مسار الملف غير صحيح');
      }

      await deleteImage(SupabaseConfig.orderImagesBucket, filePath);
    } catch (e) {
      throw Exception(StorageErrorHandler.handleStorageError(e));
    }
  }

  /// الحصول على رابط آمن لصورة طلب
  Future<String> getOrderImageSignedUrl(String orderId, String fileName) async {
    try {
      final userId = currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مسجل دخول');

      final filePath = StorageErrorHandler.createOrderFilePath(orderId, fileName);

      // التحقق من صحة المسار
      if (!StorageErrorHandler.isValidOrderFilePath(filePath, orderId)) {
        throw Exception('مسار الملف غير صحيح');
      }

      final url = await client.storage
          .from(SupabaseConfig.orderImagesBucket)
          .createSignedUrl(filePath, 60 * 60 * 24); // صالح ليوم واحد

      return url;
    } catch (e) {
      throw Exception(StorageErrorHandler.handleStorageError(e));
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // خدمات الاشتراكات المباشرة (Real-time)
  // ═══════════════════════════════════════════════════════════════

  /// الاشتراك في تحديثات الطلبات
  RealtimeChannel subscribeToOrders(String userId, Function(Map<String, dynamic>) onUpdate) {
    return client
        .channel('orders_$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: SupabaseConfig.ordersTable,
          callback: (payload) => onUpdate(payload.newRecord),
        )
        .subscribe();
  }

  /// الاشتراك في تحديثات مواقع السائقين
  RealtimeChannel subscribeToDriverLocations(Function(Map<String, dynamic>) onUpdate) {
    return client
        .channel('driver_locations')
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: SupabaseConfig.driversTable,
          callback: (payload) => onUpdate(payload.newRecord),
        )
        .subscribe();
  }

  /// إلغاء الاشتراك
  Future<void> unsubscribe(RealtimeChannel channel) async {
    await client.removeChannel(channel);
  }
}
