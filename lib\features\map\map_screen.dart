import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:async';
import 'dart:math' as math;

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/utils/language_manager.dart';
import '../../core/services/location_service.dart';
import '../../shared/services/real_data_service.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen>
    with TickerProviderStateMixin {

  // ═══════════════════════════════════════════════════════════════
  // متحكمات الأنيميشن (Animation Controllers)
  // ═══════════════════════════════════════════════════════════════

  /// متحكم أنيميشن الظهور التدريجي للعناصر
  late AnimationController _fadeController;

  /// متحكم أنيميشن الخلفية المتحركة
  late AnimationController _backgroundController;

  /// متحكم أنيميشن نبضة الموقع
  late AnimationController _locationPulseController;

  /// متحكم أنيميشن حركة المركبة
  late AnimationController _vehicleController;

  // ═══════════════════════════════════════════════════════════════
  // كائنات الأنيميشن (Animation Objects)
  // ═══════════════════════════════════════════════════════════════

  /// أنيميشن الظهور التدريجي
  late Animation<double> _fadeAnimation;

  /// أنيميشن الخلفية المتحركة
  late Animation<double> _backgroundAnimation;

  /// أنيميشن نبضة الموقع
  late Animation<double> _locationPulseAnimation;

  /// أنيميشن حركة المركبة
  late Animation<double> _vehicleAnimation;

  // ═══════════════════════════════════════════════════════════════
  // متغيرات حالة الشاشة (Screen State Variables)
  // ═══════════════════════════════════════════════════════════════

  /// حالة الملاحة (متوقفة، نشطة، مكتملة)
  String _navigationState = 'active'; // 'stopped', 'active', 'completed'

  /// المسافة المتبقية
  String _remainingDistance = '2.5 km';

  /// الوقت المتبقي
  String _remainingTime = '8 min';

  /// السرعة الحالية
  String _currentSpeed = '45 km/h';

  /// حالة GPS
  bool _isGpsActive = true;

  /// حالة الصوت
  bool _isSoundEnabled = true;

  /// مستوى التكبير
  double _zoomLevel = 15.0;

  /// إحداثيات الموقع الحالي
  double _currentLat = 33.3152;
  double _currentLng = 44.3661;

  /// حالة البوصلة
  bool _showCompass = false;

  /// نوع طبقة الخريطة
  String _currentMapLayer = 'normal'; // 'normal' أو 'satellite'

  /// خدمات الموقع والبيانات
  final LocationService _locationService = LocationService();
  Position? _currentPosition;
  bool _isLocationLoading = false;

  // ═══════════════════════════════════════════════════════════════
  // بيانات وهمية للرحلة (Mock Trip Data)
  // ═══════════════════════════════════════════════════════════════

  /// معلومات الرحلة الحالية
  final Map<String, dynamic> _currentTrip = {
    'id': 'T001',
    'customerName': 'أحمد محمد',
    'customerNameEn': 'Ahmed Mohammed',
    'customerPhone': '+964 ************',
    'from': 'شارع الرشيد',
    'fromEn': 'Al-Rashid Street',
    'to': 'مدينة الصدر',
    'toEn': 'Sadr City',
    'totalDistance': '12.5 km',
    'estimatedTime': '25 min',
    'price': 25.00,
    'status': 'في الطريق',
    'statusEn': 'On the way',
  };

  /// نقاط المسار
  final List<Map<String, dynamic>> _routePoints = [
    {'lat': 33.3152, 'lng': 44.3661, 'type': 'start'},
    {'lat': 33.3200, 'lng': 44.3700, 'type': 'waypoint'},
    {'lat': 33.3250, 'lng': 44.3750, 'type': 'waypoint'},
    {'lat': 33.3300, 'lng': 44.3800, 'type': 'end'},
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _getCurrentLocation();
  }

  /// الحصول على الموقع الحقيقي للسائق
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLocationLoading = true;
    });

    try {
      final position = await _locationService.getCurrentLocation();
      if (position != null) {
        setState(() {
          _currentPosition = position;
          _currentLat = position.latitude;
          _currentLng = position.longitude;
        });

        // تحديث موقع السائق في قاعدة البيانات
        final dataService = Provider.of<RealDataService>(context, listen: false);
        final currentUser = dataService.currentUser;
        if (currentUser != null) {
          await dataService.updateDriverLocation(currentUser.id, position);
        }
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على الموقع: $e');
    } finally {
      setState(() {
        _isLocationLoading = false;
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // التحقق من وجود بيانات الطلب من شاشة الطلبات
    final arguments = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      setState(() {
        // تحديث بيانات الرحلة الحالية
        _currentTrip['id'] = arguments['orderId'] ?? _currentTrip['id'];
        _currentTrip['customerName'] = arguments['customerName'] ?? _currentTrip['customerName'];
        _currentTrip['customerNameEn'] = arguments['customerNameEn'] ?? _currentTrip['customerNameEn'];
        _currentTrip['from'] = arguments['from'] ?? _currentTrip['from'];
        _currentTrip['fromEn'] = arguments['fromEn'] ?? _currentTrip['fromEn'];
        _currentTrip['to'] = arguments['to'] ?? _currentTrip['to'];
        _currentTrip['toEn'] = arguments['toEn'] ?? _currentTrip['toEn'];
        _currentTrip['totalDistance'] = arguments['distance'] ?? _currentTrip['totalDistance'];
        _currentTrip['price'] = arguments['price'] ?? _currentTrip['price'];

        // تحديث المسافة المتبقية
        _remainingDistance = arguments['distance'] ?? _remainingDistance;
      });
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // دالة تهيئة جميع الأنيميشن (Initialize All Animations)
  // ═══════════════════════════════════════════════════════════════
  void _initializeAnimations() {

    // أنيميشن الظهور التدريجي
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // أنيميشن الخلفية المتحركة
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );

    // أنيميشن نبضة الموقع
    _locationPulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    // أنيميشن حركة المركبة
    _vehicleController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    // إنشاء كائنات الأنيميشن
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.linear,
    ));

    _locationPulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _locationPulseController,
      curve: Curves.easeInOut,
    ));

    _vehicleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _vehicleController,
      curve: Curves.easeInOut,
    ));
  }

  // ═══════════════════════════════════════════════════════════════
  // دالة بدء جميع الأنيميشن (Start All Animations)
  // ═══════════════════════════════════════════════════════════════
  void _startAnimations() {
    _fadeController.forward();
    _backgroundController.repeat();
    _locationPulseController.repeat(reverse: true);
    _vehicleController.repeat();
  }



  @override
  void dispose() {
    _fadeController.dispose();
    _backgroundController.dispose();
    _locationPulseController.dispose();
    _vehicleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
        animation: Listenable.merge([
          _backgroundAnimation,
          _locationPulseAnimation,
          _vehicleAnimation,
        ]),
        builder: (context, child) {
          return Stack(
            children: [
              // الخلفية المتحركة
              _buildAnimatedBackground(),

              // الخريطة الوهمية
              _buildMapArea(),

              // العناصر العلوية
              SafeArea(
                child: Column(
                  children: [
                    // شريط التنقل العلوي
                    _buildAppBar(),

                    const Spacer(),

                    // معلومات الملاحة السفلية
                    _buildNavigationInfo(),
                  ],
                ),
              ),

              // أزرار التحكم الجانبية
              _buildControlButtons(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Positioned.fill(
      child: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.enhancedBackgroundGradient,
        ),
        child: CustomPaint(
          painter: MapBackgroundPainter(_backgroundAnimation.value),
        ),
      ),
    );
  }

  Widget _buildMapArea() {
    return Positioned.fill(
      child: Container(
        margin: const EdgeInsets.only(top: 120, bottom: 200),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withOpacity(0.2),
              Colors.white.withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Stack(
            children: [
              // خلفية الخريطة التفاعلية
              _buildInteractiveMapBackground(),

              // أزرار التحكم
              _buildMapControls(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInteractiveMapBackground() {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue.withOpacity(0.1),
                Colors.green.withOpacity(0.1),
                Colors.blue.withOpacity(0.05),
              ],
            ),
          ),
          child: Stack(
            children: [
              // شبكة الخريطة
              CustomPaint(
                painter: MapGridPainter(),
                size: Size.infinite,
              ),

              // مسار الرحلة المتحرك
              CustomPaint(
                painter: RoutePainter(_routePoints, _vehicleAnimation.value),
                size: Size.infinite,
              ),

              // نقاط البداية والنهاية
              _buildRoutePoints(),

              // موقع المركبة المتحرك
              _buildVehicleLocation(),

              // أزرار الخرائط
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // زر الخريطة الحقيقية
                    GestureDetector(
                      onTap: () => Navigator.pushNamed(context, '/real-map'),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppColors.success,
                              AppColors.success.withOpacity(0.8),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.success.withOpacity(0.3),
                              blurRadius: 15,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Bootstrap.geo_alt_fill,
                              color: Colors.white,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              langManager.getText('خريطة حقيقية', 'Real Map'),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // زر عرض موقعي الحالي
                    GestureDetector(
                      onTap: () => _openCurrentLocationInMaps(),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppColors.info,
                              AppColors.info.withOpacity(0.8),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.info.withOpacity(0.3),
                              blurRadius: 15,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            _isLocationLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Icon(
                                    _currentPosition != null
                                        ? Bootstrap.geo_alt_fill
                                        : Bootstrap.geo_alt,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                            const SizedBox(width: 8),
                            Text(
                              langManager.getText('موقعي الحالي', 'My Location'),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // زر فتح تطبيقات الخرائط
                    GestureDetector(
                      onTap: () => _showNavigationOptions(),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppColors.primary,
                              AppColors.primary.withOpacity(0.8),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primary.withOpacity(0.3),
                              blurRadius: 15,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.navigation,
                              color: Colors.white,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              langManager.getText('فتح الخريطة', 'Open Map'),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMapControls() {
    return Stack(
      children: [
        // أزرار التكبير والتصغير
        Positioned(
          right: 20,
          top: 20,
          child: Column(
            children: [
              _buildControlButton(
                icon: Bootstrap.plus,
                onTap: () => _zoomIn(),
              ),
              const SizedBox(height: 8),
              _buildControlButton(
                icon: Bootstrap.dash,
                onTap: () => _zoomOut(),
              ),
            ],
          ),
        ),

        // أزرار التحكم الجانبية
        Positioned(
          left: 20,
          top: 20,
          child: Column(
            children: [
              _buildControlButton(
                icon: Bootstrap.geo_alt_fill,
                onTap: () => _centerOnLocation(),
              ),
              const SizedBox(height: 12),
              _buildControlButton(
                icon: _isLocationLoading
                    ? Bootstrap.arrow_clockwise
                    : Bootstrap.arrow_repeat,
                onTap: () => _getCurrentLocation(),
              ),
              const SizedBox(height: 12),
              _buildControlButton(
                icon: Bootstrap.compass,
                onTap: () => _toggleCompass(),
              ),
              const SizedBox(height: 12),
              _buildControlButton(
                icon: Icons.navigation,
                onTap: () => _showNavigationOptions(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: AppColors.primary,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildMapBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.withOpacity(0.1),
            Colors.green.withOpacity(0.1),
            Colors.blue.withOpacity(0.05),
          ],
        ),
      ),
      child: CustomPaint(
        painter: MapGridPainter(),
        size: Size.infinite,
      ),
    );
  }

  Widget _buildRoutePoints() {
    return Stack(
      children: [
        // خط المسار
        CustomPaint(
          painter: RoutePainter(_routePoints, _vehicleAnimation.value),
          size: Size.infinite,
        ),

        // نقاط البداية والنهاية
        ..._routePoints.asMap().entries.map((entry) {
          final index = entry.key;
          final point = entry.value;
          final isStart = point['type'] == 'start';
          final isEnd = point['type'] == 'end';

          if (!isStart && !isEnd) return const SizedBox.shrink();

          return Positioned(
            left: (index / (_routePoints.length - 1)) *
                  (MediaQuery.of(context).size.width - 80) + 40,
            top: 50 + (index * 30),
            child: AnimatedBuilder(
              animation: _locationPulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: isStart ? _locationPulseAnimation.value : 1.0,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: isStart ? AppColors.success : AppColors.error,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: (isStart ? AppColors.success : AppColors.error)
                              .withOpacity(0.3),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Icon(
                      isStart ? Bootstrap.geo_alt_fill : Bootstrap.flag_fill,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                );
              },
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildVehicleLocation() {
    final progress = _vehicleAnimation.value;
    final screenWidth = MediaQuery.of(context).size.width - 80;
    final vehicleX = 40 + (progress * screenWidth);
    final vehicleY = 100 + (progress * 100);

    return Positioned(
      left: vehicleX,
      top: vehicleY,
      child: AnimatedBuilder(
        animation: _locationPulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _locationPulseAnimation.value,
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    AppColors.primary,
                    AppColors.primary.withOpacity(0.7),
                  ],
                ),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.4),
                    blurRadius: 15,
                    spreadRadius: 3,
                  ),
                ],
              ),
              child: const Icon(
                Bootstrap.car_front_fill,
                color: Colors.white,
                size: 25,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildZoomControls() {
    return Positioned(
      right: 20,
      top: 20,
      child: Column(
        children: [
          _buildZoomButton(
            icon: Bootstrap.plus,
            onTap: () => _zoomIn(),
          ),
          const SizedBox(height: 8),
          _buildZoomButton(
            icon: Bootstrap.dash,
            onTap: () => _zoomOut(),
          ),
        ],
      ),
    );
  }

  Widget _buildZoomButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Container(
            padding: const EdgeInsets.all(20.0),
            child: Row(
              children: [
                // زر الرجوع
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: const Icon(
                      Bootstrap.arrow_left,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // معلومات الرحلة
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            langManager.getText('الملاحة النشطة', 'Active Navigation'),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: _currentPosition != null
                                  ? AppColors.success
                                  : AppColors.warning,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        _currentPosition != null
                            ? langManager.getText(
                                'موقع حقيقي - إلى ${_currentTrip['to']}',
                                'Live location - To ${_currentTrip['toEn']}',
                              )
                            : langManager.getText(
                                'جاري تحديد الموقع - إلى ${_currentTrip['to']}',
                                'Locating - To ${_currentTrip['toEn']}',
                              ),
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),

                // أيقونات الحالة
                Row(
                  children: [
                    _buildStatusIcon(
                      icon: _isGpsActive ? Bootstrap.geo_alt_fill : Bootstrap.geo_alt,
                      color: _isGpsActive ? AppColors.success : AppColors.error,
                      onTap: () => _toggleGps(),
                    ),
                    const SizedBox(width: 12),
                    _buildStatusIcon(
                      icon: _isSoundEnabled ? Bootstrap.volume_up_fill : Bootstrap.volume_mute_fill,
                      color: _isSoundEnabled ? AppColors.primary : AppColors.error,
                      onTap: () => _toggleSound(),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusIcon({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color: color.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withOpacity(0.5),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: color,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildNavigationInfo() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.25),
                  Colors.white.withOpacity(0.15),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              children: [
                // معلومات العميل
                Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: AppColors.primary.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: const Icon(
                        Bootstrap.person_circle,
                        color: Colors.white,
                        size: 25,
                      ),
                    ),

                    const SizedBox(width: 16),

                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            langManager.getText(
                              _currentTrip['customerName'],
                              _currentTrip['customerNameEn'],
                            ),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            _currentTrip['customerPhone'],
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.8),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // زر الاتصال
                    GestureDetector(
                      onTap: () => _callCustomer(),
                      child: Container(
                        width: 44,
                        height: 44,
                        decoration: BoxDecoration(
                          color: AppColors.success.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppColors.success.withOpacity(0.5),
                            width: 1,
                          ),
                        ),
                        child: const Icon(
                          Bootstrap.telephone_fill,
                          color: AppColors.success,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // معلومات الملاحة
                Row(
                  children: [
                    _buildNavInfo(
                      icon: Bootstrap.geo_alt,
                      label: langManager.getText('المسافة', 'Distance'),
                      value: _remainingDistance,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: 16),
                    _buildNavInfo(
                      icon: Bootstrap.clock,
                      label: langManager.getText('الوقت', 'Time'),
                      value: _remainingTime,
                      color: AppColors.warning,
                    ),
                    const SizedBox(width: 16),
                    _buildNavInfo(
                      icon: Bootstrap.speedometer2,
                      label: langManager.getText('السرعة', 'Speed'),
                      value: _currentSpeed,
                      color: AppColors.success,
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // أزرار الإجراءات
                Row(
                  children: [
                    Expanded(
                      child: _buildActionButton(
                        title: langManager.getText('وصلت', 'Arrived'),
                        color: AppColors.success,
                        onTap: () => _markAsArrived(),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildActionButton(
                        title: langManager.getText('مشكلة', 'Issue'),
                        color: AppColors.error,
                        onTap: () => _reportIssue(),
                        isOutlined: true,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildNavInfo({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String title,
    required Color color,
    required VoidCallback onTap,
    bool isOutlined = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 14),
        decoration: BoxDecoration(
          color: isOutlined ? Colors.transparent : color,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color,
            width: 2,
          ),
        ),
        child: Text(
          title,
          style: TextStyle(
            color: isOutlined ? color : Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildControlButtons() {
    return Positioned(
      left: 20,
      top: MediaQuery.of(context).size.height * 0.4,
      child: Column(
        children: [
          _buildControlButton(
            icon: Icons.my_location,
            onTap: () => _centerOnLocation(),
          ),
          const SizedBox(height: 12),
          _buildControlButton(
            icon: Icons.explore,
            onTap: () => _toggleCompass(),
          ),
          const SizedBox(height: 12),
          _buildControlButton(
            icon: Icons.layers,
            onTap: () => _changeMapLayer(),
          ),
        ],
      ),
    );
  }



  // ═══════════════════════════════════════════════════════════════
  // دوال التحكم (Control Functions)
  // ═══════════════════════════════════════════════════════════════

  void _zoomIn() {
    setState(() {
      _zoomLevel = math.min(_zoomLevel + 0.2, 3.0);
    });
  }

  void _zoomOut() {
    setState(() {
      _zoomLevel = math.max(_zoomLevel - 0.2, 0.5);
    });
  }

  void _toggleGps() {
    setState(() {
      _isGpsActive = !_isGpsActive;
    });
  }

  void _toggleSound() {
    setState(() {
      _isSoundEnabled = !_isSoundEnabled;
    });
  }

  void _centerOnLocation() {
    if (_currentPosition != null) {
      setState(() {
        // توسيط الخريطة على الموقع الحقيقي
        _currentLat = _currentPosition!.latitude;
        _currentLng = _currentPosition!.longitude;
        _zoomLevel = 1.0;
      });

      final langManager = Provider.of<LanguageManager>(context, listen: false);
      _showSuccessMessage(
        langManager.getText('تم توسيط الخريطة على موقعك الحقيقي', 'Map centered on your real location'),
      );
    } else {
      // إذا لم يكن لدينا موقع، حاول الحصول عليه
      _getCurrentLocation();

      final langManager = Provider.of<LanguageManager>(context, listen: false);
      _showInfoMessage(
        langManager.getText('جاري تحديد موقعك...', 'Locating your position...'),
        'Locating your position...',
      );
    }
  }

  void _toggleCompass() {
    setState(() {
      _showCompass = !_showCompass;
    });

    final langManager = Provider.of<LanguageManager>(context, listen: false);
    _showInfoMessage(
      _showCompass
        ? langManager.getText('تم تفعيل البوصلة', 'Compass enabled')
        : langManager.getText('تم إلغاء تفعيل البوصلة', 'Compass disabled'),
      _showCompass ? 'Compass enabled' : 'Compass disabled',
    );
  }

  void _changeMapLayer() {
    setState(() {
      _currentMapLayer = _currentMapLayer == 'normal' ? 'satellite' : 'normal';
    });

    final langManager = Provider.of<LanguageManager>(context, listen: false);
    _showInfoMessage(
      _currentMapLayer == 'satellite'
        ? langManager.getText('تم التبديل للعرض الفضائي', 'Switched to satellite view')
        : langManager.getText('تم التبديل للعرض العادي', 'Switched to normal view'),
      _currentMapLayer == 'satellite' ? 'Switched to satellite view' : 'Switched to normal view',
    );
  }

  /// فتح موقع السائق الحالي في الخرائط الخارجية
  Future<void> _openCurrentLocationInMaps() async {
    if (_currentPosition == null) {
      final langManager = Provider.of<LanguageManager>(context, listen: false);
      _showErrorMessage(langManager.getText('لم يتم تحديد موقعك بعد', 'Your location is not determined yet'));
      return;
    }

    try {
      double currentLat = _currentPosition!.latitude;
      double currentLng = _currentPosition!.longitude;

      // رابط Google Maps لعرض الموقع الحالي
      String googleMapsUrl = 'https://www.google.com/maps/search/?api=1&query=$currentLat,$currentLng';

      bool canLaunch = await canLaunchUrl(Uri.parse(googleMapsUrl));
      if (canLaunch) {
        await launchUrl(Uri.parse(googleMapsUrl), mode: LaunchMode.externalApplication);
      } else {
        // محاولة فتح تطبيق خرائط آخر
        String fallbackUrl = 'geo:$currentLat,$currentLng?q=$currentLat,$currentLng(موقعي الحالي)';
        if (await canLaunchUrl(Uri.parse(fallbackUrl))) {
          await launchUrl(Uri.parse(fallbackUrl), mode: LaunchMode.externalApplication);
        } else {
          _showErrorMessage('لا يمكن فتح تطبيق الخرائط');
        }
      }
    } catch (e) {
      _showErrorMessage('خطأ في فتح موقعك في الخرائط');
    }
  }

  void _callCustomer() {
    final langManager = Provider.of<LanguageManager>(context, listen: false);

    // عرض رسالة تأكيد الاتصال
    _showSuccessMessage(
      langManager.getText('جاري الاتصال بالعميل...', 'Calling customer...'),
    );

    // هنا يمكن إضافة كود الاتصال الفعلي
    print('Calling customer: ${_currentTrip['customerPhone']}');
  }

  void _markAsArrived() {
    final langManager = Provider.of<LanguageManager>(context, listen: false);

    // عرض حوار تأكيد الوصول
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.success.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Bootstrap.check_circle_fill,
                  color: AppColors.success,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  langManager.getText('تأكيد الوصول', 'Confirm Arrival'),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          content: Text(
            langManager.getText(
              'هل وصلت إلى موقع العميل؟',
              'Have you arrived at the customer location?',
            ),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black54,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                langManager.getText('إلغاء', 'Cancel'),
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _confirmArrival();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: Text(
                langManager.getText('نعم، وصلت', 'Yes, Arrived'),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _confirmArrival() {
    final langManager = Provider.of<LanguageManager>(context, listen: false);

    setState(() {
      _navigationState = 'completed';
      _remainingDistance = '0 km';
      _remainingTime = '0 min';
    });

    _showSuccessMessage(
      langManager.getText('تم تأكيد الوصول بنجاح!', 'Arrival confirmed successfully!'),
    );

    // انتظار قصير ثم الانتقال إلى شاشة الطلبات مع التبويب المكتمل
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        // الانتقال إلى شاشة الطلبات مع إكمال الطلب النشط
        Navigator.pushReplacementNamed(
          context,
          '/orders',
          arguments: {
            'selectedTab': 1, // التبويب الثاني (المكتملة)
            'completeActiveTrip': true, // إشارة لإكمال الطلب النشط
            'completedTrip': {
              'id': _currentTrip['id'],
              'from': _currentTrip['from'],
              'fromEn': _currentTrip['fromEn'],
              'to': _currentTrip['to'],
              'toEn': _currentTrip['toEn'],
              'distance': _currentTrip['totalDistance'],
              'duration': _currentTrip['estimatedTime'],
              'price': _currentTrip['price'],
              'customerName': _currentTrip['customerName'],
              'customerNameEn': _currentTrip['customerNameEn'],
              'customerRating': 4.8,
              'completedTime': '${DateTime.now().hour}:${DateTime.now().minute.toString().padLeft(2, '0')}',
              'earnings': _currentTrip['price'],
              'rating': 5.0,
            }
          },
        );
      }
    });

    // يمكن إضافة المزيد من المنطق هنا مثل إرسال إشعار للعميل
    print('Trip completed successfully');
  }

  void _reportIssue() {
    final langManager = Provider.of<LanguageManager>(context, listen: false);

    // عرض قائمة خيارات المشاكل
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _buildIssueReportSheet(),
    );
  }

  Widget _buildIssueReportSheet() {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        final issues = [
          {
            'title': langManager.getText('العميل لا يرد', 'Customer not responding'),
            'icon': Bootstrap.telephone_x,
            'color': AppColors.warning,
          },
          {
            'title': langManager.getText('عنوان خاطئ', 'Wrong address'),
            'icon': Bootstrap.geo_alt_fill,
            'color': AppColors.error,
          },
          {
            'title': langManager.getText('مشكلة في المركبة', 'Vehicle issue'),
            'icon': Bootstrap.car_front,
            'color': AppColors.info,
          },
          {
            'title': langManager.getText('حالة طوارئ', 'Emergency'),
            'icon': Bootstrap.exclamation_triangle_fill,
            'color': AppColors.error,
          },
          {
            'title': langManager.getText('مشكلة أخرى', 'Other issue'),
            'icon': Bootstrap.question_circle,
            'color': AppColors.secondary,
          },
        ];

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.95),
                Colors.white.withOpacity(0.85),
              ],
            ),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // مؤشر السحب
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),

              // العنوان
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.error.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Bootstrap.exclamation_triangle_fill,
                      color: AppColors.error,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    langManager.getText('الإبلاغ عن مشكلة', 'Report an Issue'),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // قائمة المشاكل
              ...issues.map((issue) => _buildIssueOption(
                title: issue['title'] as String,
                icon: issue['icon'] as IconData,
                color: issue['color'] as Color,
                onTap: () => _handleIssueReport(issue['title'] as String),
              )).toList(),

              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  Widget _buildIssueOption({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.8),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ),
            const Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 16),
          ],
        ),
      ),
    );
  }

  void _handleIssueReport(String issueType) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);

    Navigator.pop(context); // إغلاق القائمة

    // عرض رسالة تأكيد
    _showInfoMessage(
      langManager.getText('تم إرسال التقرير بنجاح', 'Report sent successfully'),
    );

    // هنا يمكن إضافة منطق إرسال التقرير للخادم
    print('Issue reported: $issueType');
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showInfoMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.info,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال إدارة الخريطة التفاعلية (Interactive Map Management Functions)
  // ═══════════════════════════════════════════════════════════════

  /// عرض خيارات الملاحة
  void _showNavigationOptions() {

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildNavigationOptionsSheet(),
    );
  }

  Widget _buildNavigationOptionsSheet() {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.95),
                Colors.white.withOpacity(0.85),
              ],
            ),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                langManager.getText('اختر تطبيق الملاحة', 'Choose Navigation App'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 20),

              // Google Maps
              _buildNavigationOption(
                icon: Icons.map,
                title: 'Google Maps',
                subtitle: langManager.getText('فتح في خرائط جوجل', 'Open in Google Maps'),
                onTap: () => _openInGoogleMaps(),
              ),

              const SizedBox(height: 12),

              // Waze
              _buildNavigationOption(
                icon: Icons.navigation,
                title: 'Waze',
                subtitle: langManager.getText('فتح في تطبيق Waze', 'Open in Waze'),
                onTap: () => _openInWaze(),
              ),

              const SizedBox(height: 12),

              // Apple Maps (iOS only)
              if (Theme.of(context).platform == TargetPlatform.iOS)
                _buildNavigationOption(
                  icon: Icons.map_outlined,
                  title: 'Apple Maps',
                  subtitle: langManager.getText('فتح في خرائط آبل', 'Open in Apple Maps'),
                  onTap: () => _openInAppleMaps(),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNavigationOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.8),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, color: AppColors.primary, size: 24),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 16),
          ],
        ),
      ),
    );
  }

  /// فتح في Google Maps
  Future<void> _openInGoogleMaps() async {
    try {
      String googleMapsUrl;

      if (_currentPosition != null) {
        // إذا كان لدينا موقع حقيقي، استخدمه
        double currentLat = _currentPosition!.latitude;
        double currentLng = _currentPosition!.longitude;

        // إحداثيات الوجهة (مدينة الصدر، بغداد)
        double destinationLat = 33.3300;
        double destinationLng = 44.3800;

        // رابط Google Maps مع الموقع الحالي والوجهة
        googleMapsUrl = 'https://www.google.com/maps/dir/$currentLat,$currentLng/$destinationLat,$destinationLng';
      } else {
        // إذا لم يكن لدينا موقع، استخدم الوجهة فقط
        double destinationLat = 33.3300;
        double destinationLng = 44.3800;
        googleMapsUrl = 'https://www.google.com/maps/search/?api=1&query=$destinationLat,$destinationLng';
      }

      bool canLaunch = await canLaunchUrl(Uri.parse(googleMapsUrl));
      if (canLaunch) {
        await launchUrl(Uri.parse(googleMapsUrl), mode: LaunchMode.externalApplication);
      } else {
        _showErrorMessage('لا يمكن فتح تطبيق الخرائط');
      }
    } catch (e) {
      _showErrorMessage('خطأ في فتح تطبيق الخرائط');
    }
  }

  /// فتح في Waze
  Future<void> _openInWaze() async {
    // إحداثيات الوجهة (مدينة الصدر، بغداد)
    double destinationLat = 33.3300;
    double destinationLng = 44.3800;

    String wazeUrl = 'waze://?ll=$destinationLat,$destinationLng&navigate=yes';

    try {
      bool canLaunch = await canLaunchUrl(Uri.parse(wazeUrl));
      if (canLaunch) {
        await launchUrl(Uri.parse(wazeUrl), mode: LaunchMode.externalApplication);
      } else {
        _showErrorMessage('تطبيق Waze غير مثبت');
      }
    } catch (e) {
      _showErrorMessage('خطأ في فتح تطبيق Waze');
    }
  }

  /// فتح في Apple Maps
  Future<void> _openInAppleMaps() async {
    try {
      String appleMapsUrl;

      if (_currentPosition != null) {
        // إذا كان لدينا موقع حقيقي، استخدمه
        double currentLat = _currentPosition!.latitude;
        double currentLng = _currentPosition!.longitude;

        // إحداثيات الوجهة (مدينة الصدر، بغداد)
        double destinationLat = 33.3300;
        double destinationLng = 44.3800;

        // رابط Apple Maps مع الموقع الحالي والوجهة
        appleMapsUrl = 'maps://?saddr=$currentLat,$currentLng&daddr=$destinationLat,$destinationLng&dirflg=d';
      } else {
        // إذا لم يكن لدينا موقع، استخدم الوجهة فقط
        double destinationLat = 33.3300;
        double destinationLng = 44.3800;
        appleMapsUrl = 'maps://?daddr=$destinationLat,$destinationLng&dirflg=d';
      }

      bool canLaunch = await canLaunchUrl(Uri.parse(appleMapsUrl));
      if (canLaunch) {
        await launchUrl(Uri.parse(appleMapsUrl), mode: LaunchMode.externalApplication);
      } else {
        _showErrorMessage('تطبيق Apple Maps غير متاح');
      }
    } catch (e) {
      _showErrorMessage('خطأ في فتح تطبيق Apple Maps');
    }
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}

// ═══════════════════════════════════════════════════════════════
// فئات الرسم المخصص (Custom Painters)
// ═══════════════════════════════════════════════════════════════

/// رسام الخلفية المتحركة للخريطة
class MapBackgroundPainter extends CustomPainter {
  final double animationValue;

  MapBackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // رسم دوائر متحركة تمثل الموجات
    _drawRadarWaves(canvas, size, paint);

    // رسم خطوط الشبكة
    _drawGridLines(canvas, size, paint);

    // رسم نقاط متلألئة
    _drawTwinklingDots(canvas, size, paint);
  }

  void _drawRadarWaves(Canvas canvas, Size size, Paint paint) {
    final center = Offset(size.width * 0.5, size.height * 0.5);

    for (int i = 0; i < 3; i++) {
      final radius = (50.0 + i * 30) * (1 + animationValue * 0.5);
      final opacity = (0.05 - i * 0.01) * (1 - animationValue);

      paint.color = Colors.white.withOpacity(opacity);
      canvas.drawCircle(center, radius, paint);
    }
  }

  void _drawGridLines(Canvas canvas, Size size, Paint paint) {
    paint
      ..color = Colors.white.withOpacity(0.02)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    // خطوط عمودية
    for (double x = 0; x <= size.width; x += 40) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // خطوط أفقية
    for (double y = 0; y <= size.height; y += 40) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  void _drawTwinklingDots(Canvas canvas, Size size, Paint paint) {
    paint.style = PaintingStyle.fill;

    for (int i = 0; i < 15; i++) {
      final x = size.width * (i % 5) / 5 + (size.width * 0.1);
      final y = size.height * (i ~/ 5) / 3 + (size.height * 0.1);

      final opacity = (math.sin(animationValue * 3 * math.pi + i) + 1) / 2;
      paint.color = Colors.white.withOpacity(opacity * 0.04);

      canvas.drawCircle(Offset(x, y), 1.0, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// رسام شبكة الخريطة
class MapGridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.03)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    // رسم شبكة مربعات
    const gridSize = 30.0;

    // خطوط عمودية
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // خطوط أفقية
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // رسم خطوط رئيسية أكثر وضوحاً
    paint.color = Colors.white.withOpacity(0.05);
    paint.strokeWidth = 1.0;

    // خط وسط عمودي
    canvas.drawLine(
      Offset(size.width / 2, 0),
      Offset(size.width / 2, size.height),
      paint,
    );

    // خط وسط أفقي
    canvas.drawLine(
      Offset(0, size.height / 2),
      Offset(size.width, size.height / 2),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// رسام مسار الرحلة
class RoutePainter extends CustomPainter {
  final List<Map<String, dynamic>> routePoints;
  final double animationValue;

  RoutePainter(this.routePoints, this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    if (routePoints.length < 2) return;

    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.0
      ..strokeCap = StrokeCap.round;

    final path = Path();
    final animatedPath = Path();

    // إنشاء المسار الكامل
    for (int i = 0; i < routePoints.length; i++) {
      final x = (i / (routePoints.length - 1)) * (size.width - 80) + 40;
      final y = 50 + (i * 30) + math.sin(i * 0.5) * 20;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        // إنشاء منحنى ناعم
        final prevX = ((i - 1) / (routePoints.length - 1)) * (size.width - 80) + 40;
        final prevY = 50 + ((i - 1) * 30) + math.sin((i - 1) * 0.5) * 20;

        final controlX = (prevX + x) / 2;
        final controlY = (prevY + y) / 2 - 20;

        path.quadraticBezierTo(controlX, controlY, x, y);
      }
    }

    // رسم المسار المكتمل بشفافية
    paint.color = AppColors.primary.withOpacity(0.3);
    canvas.drawPath(path, paint);

    // رسم المسار المتحرك
    final pathMetrics = path.computeMetrics();
    for (final metric in pathMetrics) {
      final extractedPath = metric.extractPath(
        0.0,
        metric.length * animationValue,
      );

      paint.color = AppColors.primary;
      paint.shader = LinearGradient(
        colors: [
          AppColors.primary,
          AppColors.primary.withOpacity(0.5),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

      canvas.drawPath(extractedPath, paint);
    }

    // رسم نقاط الطريق
    paint.shader = null;
    for (int i = 1; i < routePoints.length - 1; i++) {
      final x = (i / (routePoints.length - 1)) * (size.width - 80) + 40;
      final y = 50 + (i * 30) + math.sin(i * 0.5) * 20;

      paint.color = AppColors.warning;
      paint.style = PaintingStyle.fill;
      canvas.drawCircle(Offset(x, y), 6, paint);

      paint.color = Colors.white;
      canvas.drawCircle(Offset(x, y), 3, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}