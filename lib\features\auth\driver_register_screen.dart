import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/utils/language_manager.dart';
import '../../shared/widgets/custom_text_field.dart';
import '../../shared/providers/auth_provider.dart';
import '../../shared/constants/vehicle_types.dart';
import '../../shared/utils/validators.dart';
import '../../shared/widgets/loading_button.dart';

class DriverRegisterScreen extends StatefulWidget {
  const DriverRegisterScreen({super.key});

  @override
  State<DriverRegisterScreen> createState() => _DriverRegisterScreenState();
}

class _DriverRegisterScreenState extends State<DriverRegisterScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  final List<GlobalKey<FormState>> _formKeys = [
    GlobalKey<FormState>(),
    GlobalKey<FormState>(),
    GlobalKey<FormState>(),
    GlobalKey<FormState>(),
  ];

  // Controllers للخطوة الأولى - المعلومات الشخصية
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // Controllers للخطوة الثانية - معلومات المركبة
  final _plateNumberController = TextEditingController();
  final _manufacturingYearController = TextEditingController();
  final _maxLoadController = TextEditingController();
  final _drivingLicenseController = TextEditingController();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  int _currentStep = 0;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;
  bool _agreeToTerms = false;
  String? _selectedVehicleType;
  String? _selectedVehicleModel;
  DateTime? _licenseExpiryDate;

  // استخدام قائمة أنواع المركبات الموحدة
  final List<String> _vehicleTypes = VehicleTypes.standardTypes;

  // قائمة نماذج المركبات
  final List<String> _vehicleModels = [
    'هيونداي',
    'كيا',
    'تويوتا',
    'نيسان',
    'ميتسوبيشي',
    'إيسوزو',
    'فولفو',
    'مرسيدس',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _plateNumberController.dispose();
    _manufacturingYearController.dispose();
    _maxLoadController.dispose();
    _drivingLicenseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.enhancedBackgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // شريط التنقل العلوي
              _buildAppBar(),

              // مؤشر التقدم
              _buildProgressIndicator(),

              // محتوى الخطوات
              Expanded(
                child: PageView(
                  controller: _pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _buildPersonalInfoStep(),
                    _buildVehicleInfoStep(),
                    _buildDocumentsStep(),
                    _buildReviewStep(),
                  ],
                ),
              ),

              // أزرار التنقل
              _buildNavigationButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          IconButton(
            onPressed: () {
              if (_currentStep > 0) {
                _previousStep();
              } else {
                Navigator.pushReplacementNamed(context, '/driver-login');
              }
            },
            icon: const Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
            ),
          ),
          Expanded(
            child: Consumer<LanguageManager>(
              builder: (context, langManager, child) {
                return Text(
                  langManager.getText(AppStrings.registerTitle, 'Create New Account'),
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                );
              },
            ),
          ),
          const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        children: List.generate(4, (index) {
          final isActive = index <= _currentStep;
          final isCompleted = index < _currentStep;

          return Expanded(
            child: Container(
              margin: EdgeInsets.only(right: index < 3 ? 8 : 0),
              child: Column(
                children: [
                  // الدائرة
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: isActive ? Colors.white : Colors.white.withOpacity(0.3),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: isCompleted
                          ? const Icon(
                              Icons.check,
                              color: AppColors.primary,
                              size: 18,
                            )
                          : Text(
                              '${index + 1}',
                              style: TextStyle(
                                color: isActive ? AppColors.primary : Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                    ),
                  ),

                  const SizedBox(height: 8),

                  // الخط
                  if (index < 3)
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      height: 2,
                      color: isCompleted ? Colors.white : Colors.white.withOpacity(0.3),
                    ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildPersonalInfoStep() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.4),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKeys[0],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return Text(
                      langManager.getText(AppStrings.personalInfo, 'Personal Information'),
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.3),
                            offset: const Offset(1, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                    );
                  },
                ),

                const SizedBox(height: 24),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return CustomTextField(
                      controller: _firstNameController,
                      label: langManager.getText(AppStrings.firstName, 'First Name'),
                      hint: langManager.getText('أدخل الاسم الأول', 'Enter first name'),
                      prefixIcon: Bootstrap.person,
                      isDarkMode: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return langManager.getText('الاسم الأول مطلوب', 'First name is required');
                        }
                        return null;
                      },
                    );
                  },
                ),

                const SizedBox(height: 16),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return CustomTextField(
                      controller: _lastNameController,
                      label: langManager.getText(AppStrings.lastName, 'Last Name'),
                      hint: langManager.getText('أدخل الاسم الأخير', 'Enter last name'),
                      prefixIcon: Bootstrap.person,
                      isDarkMode: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return langManager.getText('الاسم الأخير مطلوب', 'Last name is required');
                        }
                        return null;
                      },
                    );
                  },
                ),

                const SizedBox(height: 16),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return CustomTextField(
                      controller: _phoneController,
                      label: langManager.getText(AppStrings.phoneNumber, 'Phone Number'),
                      hint: langManager.getText(AppStrings.phoneHint, '07xxxxxxxxx'),
                      prefixIcon: Bootstrap.phone,
                      keyboardType: TextInputType.phone,
                      isDarkMode: true,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(11),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return langManager.getText(AppStrings.phoneRequired, 'Phone number is required');
                        }
                        if (value.length != 11 || !value.startsWith('07')) {
                          return langManager.getText(AppStrings.invalidPhone, 'Invalid phone number');
                        }
                        return null;
                      },
                    );
                  },
                ),

                const SizedBox(height: 16),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return CustomTextField(
                      controller: _passwordController,
                      label: langManager.getText(AppStrings.password, 'Password'),
                      hint: langManager.getText(AppStrings.passwordHint, 'Enter password'),
                      prefixIcon: Bootstrap.lock,
                      suffixIcon: _isPasswordVisible ? Bootstrap.eye_slash : Bootstrap.eye,
                      obscureText: !_isPasswordVisible,
                      isDarkMode: true,
                      onSuffixTap: () {
                        setState(() {
                          _isPasswordVisible = !_isPasswordVisible;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return langManager.getText(AppStrings.passwordRequired, 'Password is required');
                        }
                        if (value.length < 6) {
                          return langManager.getText(AppStrings.passwordTooShort, 'Password is too short');
                        }
                        return null;
                      },
                    );
                  },
                ),

                const SizedBox(height: 16),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return CustomTextField(
                      controller: _confirmPasswordController,
                      label: langManager.getText(AppStrings.confirmPassword, 'Confirm Password'),
                      hint: langManager.getText('أعد إدخال كلمة المرور', 'Re-enter password'),
                      prefixIcon: Bootstrap.lock,
                      suffixIcon: _isConfirmPasswordVisible ? Bootstrap.eye_slash : Bootstrap.eye,
                      obscureText: !_isConfirmPasswordVisible,
                      isDarkMode: true,
                      onSuffixTap: () {
                        setState(() {
                          _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return langManager.getText('تأكيد كلمة المرور مطلوب', 'Confirm password is required');
                        }
                        if (value != _passwordController.text) {
                          return langManager.getText(AppStrings.passwordMismatch, 'Passwords do not match');
                        }
                        return null;
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVehicleInfoStep() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKeys[1],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return Text(
                      langManager.getText(AppStrings.vehicleInfo, 'Vehicle Information'),
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),

                const SizedBox(height: 24),

                // نوع الشاحنة
                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          langManager.getText(AppStrings.vehicleType, 'Vehicle Type'),
                          style: Theme.of(context).textTheme.labelMedium?.copyWith(
                            color: Colors.white.withOpacity(0.8),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: DropdownButtonFormField<String>(
                            value: _selectedVehicleType,
                            decoration: const InputDecoration(
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                              prefixIcon: Icon(Bootstrap.truck, color: AppColors.primary),
                            ),
                            hint: Text(langManager.getText(AppStrings.selectVehicleType, 'Select vehicle type')),
                            items: _vehicleTypes.map((String type) {
                              return DropdownMenuItem<String>(
                                value: type,
                                child: Text(type),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              setState(() {
                                _selectedVehicleType = newValue;
                              });
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return langManager.getText(AppStrings.vehicleTypeRequired, 'Vehicle type is required');
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    );
                  },
                ),

                const SizedBox(height: 16),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return CustomTextField(
                      controller: _plateNumberController,
                      label: langManager.getText(AppStrings.plateNumber, 'Plate Number'),
                      hint: langManager.getText(AppStrings.plateNumberExample, 'Example: Baghdad 123456'),
                      prefixIcon: Bootstrap.credit_card,
                      isDarkMode: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return langManager.getText(AppStrings.plateNumberRequired, 'Plate number is required');
                        }
                        return null;
                      },
                    );
                  },
                ),

                const SizedBox(height: 16),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return CustomTextField(
                      controller: _manufacturingYearController,
                      label: langManager.getText(AppStrings.manufacturingYear, 'Manufacturing Year'),
                      hint: langManager.getText(AppStrings.yearExample, 'Example: 2020'),
                      prefixIcon: Bootstrap.calendar,
                      keyboardType: TextInputType.number,
                      isDarkMode: true,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(4),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return langManager.getText(AppStrings.yearRequired, 'Manufacturing year is required');
                        }
                        final year = int.tryParse(value);
                        if (year == null || year < 1990 || year > DateTime.now().year) {
                          return langManager.getText(AppStrings.invalidYear, 'Invalid manufacturing year');
                        }
                        return null;
                      },
                    );
                  },
                ),

                const SizedBox(height: 16),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return CustomTextField(
                      controller: _maxLoadController,
                      label: langManager.getText(AppStrings.maxLoad, 'Max Load'),
                      hint: langManager.getText(AppStrings.loadExample, 'Example: 5 tons'),
                      prefixIcon: Bootstrap.box,
                      keyboardType: TextInputType.number,
                      isDarkMode: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return langManager.getText(AppStrings.loadRequired, 'Max load is required');
                        }
                        return null;
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentsStep() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKeys[2],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return Text(
                      langManager.getText(AppStrings.documents, 'Documents'),
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),

                const SizedBox(height: 24),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return _buildDocumentUpload(
                      title: langManager.getText(AppStrings.profilePhoto, 'Profile Photo'),
                      subtitle: langManager.getText(AppStrings.profilePhotoSubtitle, 'Clear personal photo'),
                      icon: Bootstrap.person_circle,
                      langManager: langManager,
                    );
                  },
                ),

                const SizedBox(height: 16),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return _buildDocumentUpload(
                      title: langManager.getText(AppStrings.drivingLicense, 'Driving License'),
                      subtitle: langManager.getText(AppStrings.licenseSubtitle, 'Driving license photo'),
                      icon: Bootstrap.credit_card,
                      langManager: langManager,
                    );
                  },
                ),

                const SizedBox(height: 16),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return _buildDocumentUpload(
                      title: langManager.getText(AppStrings.vehiclePhotos, 'Vehicle Photos'),
                      subtitle: langManager.getText(AppStrings.vehiclePhotosSubtitle, 'Vehicle photos from all sides'),
                      icon: Bootstrap.camera,
                      multiple: true,
                      langManager: langManager,
                    );
                  },
                ),

                const SizedBox(height: 16),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return _buildDocumentUpload(
                      title: langManager.getText(AppStrings.additionalDocs, 'Additional Documents'),
                      subtitle: langManager.getText(AppStrings.additionalDocsSubtitle, 'Insurance, inspection, etc'),
                      icon: Bootstrap.file_earmark,
                      optional: true,
                      langManager: langManager,
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentUpload({
    required String title,
    required String subtitle,
    required IconData icon,
    required LanguageManager langManager,
    bool multiple = false,
    bool optional = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.4),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.25),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          subtitle + (optional ? ' (${langManager.getText(AppStrings.optional, 'Optional')})' : ''),
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 13,
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.25),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Text(
            langManager.getText(AppStrings.upload, 'Upload'),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 13,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        onTap: () {
          _showImagePicker(multiple: multiple);
        },
      ),
    );
  }

  Widget _buildReviewStep() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKeys[3],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return Text(
                      langManager.getText(AppStrings.review, 'Review'),
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),

                const SizedBox(height: 24),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return _buildReviewSection(
                      title: langManager.getText('المعلومات الشخصية', 'Personal Information'),
                      items: [
                        '${langManager.getText('الاسم', 'Name')}: ${_firstNameController.text} ${_lastNameController.text}',
                        '${langManager.getText('رقم الهاتف', 'Phone Number')}: ${_phoneController.text}',
                      ],
                      langManager: langManager,
                    );
                  },
                ),

                const SizedBox(height: 16),

                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return _buildReviewSection(
                      title: langManager.getText('معلومات المركبة', 'Vehicle Information'),
                      items: [
                        '${langManager.getText('نوع الشاحنة', 'Vehicle Type')}: ${_selectedVehicleType ?? langManager.getText('غير محدد', 'Not specified')}',
                        '${langManager.getText('رقم اللوحة', 'Plate Number')}: ${_plateNumberController.text}',
                        '${langManager.getText('سنة الصنع', 'Manufacturing Year')}: ${_manufacturingYearController.text}',
                        '${langManager.getText('الحمولة القصوى', 'Max Load')}: ${_maxLoadController.text}',
                      ],
                      langManager: langManager,
                    );
                  },
                ),

                const SizedBox(height: 24),

                // موافقة على الشروط والأحكام
                Consumer<LanguageManager>(
                  builder: (context, langManager, child) {
                    return Row(
                      children: [
                        Checkbox(
                          value: _agreeToTerms,
                          onChanged: (value) {
                            setState(() {
                              _agreeToTerms = value ?? false;
                            });
                          },
                          fillColor: MaterialStateProperty.all(Colors.white),
                          checkColor: AppColors.primary,
                        ),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _agreeToTerms = !_agreeToTerms;
                              });
                            },
                            child: Text(
                              langManager.getText(AppStrings.agreeToTerms, 'I agree to the terms and conditions'),
                              style: const TextStyle(
                                color: Colors.white,
                                decoration: TextDecoration.underline,
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReviewSection({
    required String title,
    required List<String> items,
    required LanguageManager langManager,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.4),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 17,
            ),
          ),
          const SizedBox(height: 14),
          ...items.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 10),
            child: Text(
              item,
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Container(
          padding: const EdgeInsets.all(24),
          child: Row(
            children: [
              if (_currentStep > 0)
                Expanded(
                  child: OutlinedButton(
                    onPressed: _previousStep,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.white,
                      side: const BorderSide(color: Colors.white),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: Text(langManager.getText(AppStrings.previous, 'Previous')),
                  ),
                ),

              if (_currentStep > 0) const SizedBox(width: 16),

              Expanded(
                child: LoadingButton(
                  onPressed: _currentStep == 3 ? _submitRegistration : _nextStep,
                  isLoading: _isLoading,
                  text: _currentStep == 3
                    ? langManager.getText(AppStrings.submitRegistration, 'Submit Registration')
                    : langManager.getText(AppStrings.next, 'Next'),
                  backgroundColor: Colors.white,
                  textColor: AppColors.primary,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _nextStep() {
    if (_formKeys[_currentStep].currentState!.validate()) {
      if (_currentStep < 3) {
        setState(() {
          _currentStep++;
        });
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _submitRegistration() async {
    if (!_formKeys[3].currentState!.validate()) return;

    if (!_agreeToTerms) {
      final langManager = Provider.of<LanguageManager>(context, listen: false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(langManager.getText(AppStrings.agreeTermsRequired, 'You must agree to terms and conditions')),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final langManager = Provider.of<LanguageManager>(context, listen: false);

      // إنشاء إيميل مؤقت من رقم الهاتف
      final tempEmail = '${_phoneController.text.trim()}@hamoolati.temp';

      // استدعاء تسجيل السائق الحقيقي
      final success = await authProvider.registerDriver(
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        email: tempEmail, // إيميل مؤقت صحيح
        phone: _phoneController.text.trim(),
        password: _passwordController.text,
        vehicleType: _selectedVehicleType ?? 'أتيكو 6 متر',
        plateNumber: _plateNumberController.text.trim(),
        vehicleModel: _selectedVehicleModel ?? 'هيونداي',
        manufacturingYear: int.tryParse(_manufacturingYearController.text) ?? DateTime.now().year,
        drivingLicenseNumber: _drivingLicenseController.text.trim(),
        licenseExpiryDate: _licenseExpiryDate ?? DateTime.now().add(const Duration(days: 365)),
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(langManager.getText(AppStrings.registrationSubmitted, 'Registration submitted successfully')),
              backgroundColor: AppColors.success,
            ),
          );

          Navigator.pushReplacementNamed(context, '/driver-login');
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(authProvider.errorMessage ?? langManager.getText('حدث خطأ أثناء التسجيل', 'An error occurred during registration')),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        final langManager = Provider.of<LanguageManager>(context, listen: false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(langManager.getText('حدث خطأ أثناء التسجيل', 'An error occurred during registration')),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showImagePicker({bool multiple = false}) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              langManager.getText(
                'اختيار ${multiple ? 'الصور' : 'الصورة'}',
                'Select ${multiple ? 'Images' : 'Image'}'
              ),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const Icon(Bootstrap.camera),
                    label: Text(langManager.getText('الكاميرا', 'Camera')),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const Icon(Bootstrap.image),
                    label: Text(langManager.getText('المعرض', 'Gallery')),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}