import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/utils/language_manager.dart';
import '../../shared/providers/auth_provider.dart';
import '../../shared/models/user.dart';
import '../../shared/widgets/custom_text_field.dart';
import '../../shared/widgets/loading_button.dart';

class DriverLoginScreen extends StatefulWidget {
  const DriverLoginScreen({super.key});

  @override
  State<DriverLoginScreen> createState() => _DriverLoginScreenState();
}

class _DriverLoginScreenState extends State<DriverLoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // أنيميشن الخلفية
  late AnimationController _backgroundController;
  late Animation<double> _backgroundAnimation;

  bool _isPasswordVisible = false;
  bool _rememberMe = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    // أنيميشن الخلفية المتحرك
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.linear,
    ));
  }

  void _startAnimations() {
    _animationController.forward();
    // إيقاف أنيميشن الخلفية المتكرر
    // _backgroundController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _backgroundController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // الخلفية الأساسية تغطي كامل الشاشة
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                gradient: AppColors.enhancedBackgroundGradient,
              ),
            ),
          ),

              // المحتوى الرئيسي
              SafeArea(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height -
                                 MediaQuery.of(context).padding.top -
                                 MediaQuery.of(context).padding.bottom,
                    ),
                    child: Column(
                      children: [
                        // شريط التنقل العلوي
                        _buildAppBar(),

                        const SizedBox(height: 40),

                        // أيقونة السائق
                        _buildDriverIcon(),

                        const SizedBox(height: 32),

                        // نموذج تسجيل الدخول
                        _buildLoginForm(),

                        const SizedBox(height: 24),

                        // رابط إنشاء حساب جديد
                        _buildCreateAccountLink(),

                        // مساحة إضافية للتأكد من تغطية كامل الشاشة
                        const SizedBox(height: 50),
                      ],
                    ),
                  ),
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Positioned.fill(
      child: CustomPaint(
        painter: AnimatedBackgroundPainter(_backgroundAnimation.value),
      ),
    );
  }

  Widget _buildAppBar() {
    return Row(
      children: [
        IconButton(
          onPressed: () {
            Navigator.pushReplacementNamed(context, '/splash');
          },
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.white,
          ),
        ),
        Expanded(
          child: Consumer<LanguageManager>(
            builder: (context, langManager, child) {
              return Text(
                '${AppTexts.loginTitle(langManager)} - ${AppTexts.driverOption(langManager)}',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              );
            },
          ),
        ),
        const SizedBox(width: 48), // للتوازن
      ],
    );
  }

  Widget _buildDriverIcon() {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white,
                  Colors.white.withOpacity(0.9),
                ],
              ),
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.15),
                  blurRadius: 25,
                  offset: const Offset(0, 12),
                ),
                BoxShadow(
                  color: Colors.white.withOpacity(0.1),
                  blurRadius: 15,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: const Icon(
              Bootstrap.person_circle,
              size: 60,
              color: AppColors.primary,
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoginForm() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.15),
                    Colors.white.withOpacity(0.08),
                    Colors.white.withOpacity(0.05),
                  ],
                ),
                border: Border.all(
                  color: Colors.white.withOpacity(0.4),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 30,
                    offset: const Offset(0, 15),
                  ),
                  BoxShadow(
                    color: Colors.white.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, -8),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // العنوان
                      Consumer<LanguageManager>(
                        builder: (context, langManager, child) {
                          return Text(
                            langManager.getText(AppStrings.loginSubtitle, 'Enter your credentials to login'),
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 24),

                      // حقل رقم الهاتف
                      Consumer<LanguageManager>(
                        builder: (context, langManager, child) {
                          return CustomTextField(
                            controller: _phoneController,
                            label: langManager.getText(AppStrings.phoneNumber, 'Phone Number'),
                            hint: langManager.getText(AppStrings.phoneHint, '07xxxxxxxxx'),
                            prefixIcon: Bootstrap.phone,
                            keyboardType: TextInputType.phone,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(11),
                            ],
                            validator: _validatePhone,
                          );
                        },
                      ),

                      const SizedBox(height: 16),

                      // حقل كلمة المرور
                      Consumer<LanguageManager>(
                        builder: (context, langManager, child) {
                          return CustomTextField(
                            controller: _passwordController,
                            label: langManager.getText(AppStrings.password, 'Password'),
                            hint: langManager.getText(AppStrings.passwordHint, 'Enter password'),
                            prefixIcon: Bootstrap.lock,
                            suffixIcon: _isPasswordVisible
                                ? Bootstrap.eye_slash
                                : Bootstrap.eye,
                            obscureText: !_isPasswordVisible,
                            onSuffixTap: () {
                              setState(() {
                                _isPasswordVisible = !_isPasswordVisible;
                              });
                            },
                            validator: _validatePassword,
                          );
                        },
                      ),

                      const SizedBox(height: 16),

                      // تذكرني ونسيت كلمة المرور
                      Consumer<LanguageManager>(
                        builder: (context, langManager, child) {
                          return Row(
                            children: [
                              Checkbox(
                                value: _rememberMe,
                                onChanged: (value) {
                                  setState(() {
                                    _rememberMe = value ?? false;
                                  });
                                },
                                fillColor: MaterialStateProperty.all(Colors.white),
                                checkColor: AppColors.primary,
                              ),
                              Text(
                                langManager.getText(AppStrings.rememberMe, 'Remember me'),
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                              const Spacer(),
                              TextButton(
                                onPressed: _showForgotPasswordDialog,
                                child: Text(
                                  langManager.getText(AppStrings.forgotPassword, 'Forgot password?'),
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.white,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),

                      const SizedBox(height: 24),

                      // زر تسجيل الدخول
                      Consumer<LanguageManager>(
                        builder: (context, langManager, child) {
                          return LoadingButton(
                            onPressed: _handleLogin,
                            isLoading: _isLoading,
                            text: langManager.getText(AppStrings.login, 'Login'),
                            backgroundColor: Colors.white,
                            textColor: AppColors.primary,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCreateAccountLink() {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Consumer<LanguageManager>(
            builder: (context, langManager, child) {
              return TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/driver-register');
                },
                child: RichText(
                  text: TextSpan(
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withOpacity(0.8),
                    ),
                    children: [
                      TextSpan(text: langManager.getText('ليس لديك حساب؟ ', 'Don\'t have an account? ')),
                      TextSpan(
                        text: langManager.getText(AppStrings.createAccount, 'Create Account'),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  String? _validatePhone(String? value) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    if (value == null || value.isEmpty) {
      return langManager.getText(AppStrings.phoneRequired, 'Phone number is required');
    }
    if (value.length != 11 || !value.startsWith('07')) {
      return langManager.getText(AppStrings.invalidPhone, 'Invalid phone number');
    }
    return null;
  }

  String? _validatePassword(String? value) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    if (value == null || value.isEmpty) {
      return langManager.getText(AppStrings.passwordRequired, 'Password is required');
    }
    if (value.length < 6) {
      return langManager.getText(AppStrings.passwordTooShort, 'Password is too short');
    }
    return null;
  }

  void _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final langManager = Provider.of<LanguageManager>(context, listen: false);

      // محاولة تسجيل الدخول الحقيقي
      final success = await authProvider.login(
        email: _phoneController.text.trim(),
        password: _passwordController.text,
        userType: UserType.driver,
        rememberMe: _rememberMe,
      );

      if (success && mounted) {
        // إظهار رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(langManager.getText(AppStrings.loginSuccess, 'Login successful')),
            backgroundColor: AppColors.success,
          ),
        );

        // الانتقال للوحة التحكم
        Navigator.pushReplacementNamed(context, '/dashboard');
      } else if (mounted) {
        // فشل تسجيل الدخول - عرض رسالة الخطأ
        final errorMessage = authProvider.errorMessage ??
            langManager.getText(AppStrings.invalidCredentials, 'Invalid credentials');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        final langManager = Provider.of<LanguageManager>(context, listen: false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(langManager.getText('حدث خطأ أثناء تسجيل الدخول', 'Login error occurred')),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showForgotPasswordDialog() {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(langManager.getText(AppStrings.forgotPassword, 'Forgot Password')),
        content: Text(langManager.getText(AppStrings.forgotPasswordMessage, 'A password reset link will be sent to your phone number')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(langManager.getText(AppStrings.cancel, 'Cancel')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // إرسال رابط إعادة التعيين
            },
            child: Text(langManager.getText(AppStrings.send, 'Send')),
          ),
        ],
      ),
    );
  }
}

// فئة الرسام المخصص للخلفية المتحركة
class AnimatedBackgroundPainter extends CustomPainter {
  final double animationValue;

  AnimatedBackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    // رسم دوائر متحركة
    _drawMovingCircles(canvas, size, paint);

    // رسم خطوط متموجة
    _drawWavyLines(canvas, size, paint);

    // رسم نقاط متلألئة
    _drawTwinklingDots(canvas, size, paint);
  }

  void _drawMovingCircles(Canvas canvas, Size size, Paint paint) {
    // دائرة كبيرة متحركة
    final circle1X = size.width * 0.2 + (size.width * 0.6 * animationValue);
    final circle1Y = size.height * 0.3;
    paint.color = Colors.white.withOpacity(0.03);
    canvas.drawCircle(Offset(circle1X, circle1Y), 80, paint);

    // دائرة متوسطة متحركة
    final circle2X = size.width * 0.8 - (size.width * 0.4 * animationValue);
    final circle2Y = size.height * 0.7;
    paint.color = Colors.white.withOpacity(0.05);
    canvas.drawCircle(Offset(circle2X, circle2Y), 60, paint);

    // دوائر صغيرة متحركة
    for (int i = 0; i < 5; i++) {
      final x = size.width * (0.1 + (i * 0.2)) +
                (30 * math.sin(animationValue * 2 * math.pi + i));
      final y = size.height * (0.2 + (i * 0.15)) +
                (20 * math.cos(animationValue * 2 * math.pi + i));
      paint.color = Colors.white.withOpacity(0.02);
      canvas.drawCircle(Offset(x, y), 25, paint);
    }
  }

  void _drawWavyLines(Canvas canvas, Size size, Paint paint) {
    paint
      ..color = Colors.white.withOpacity(0.04)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();
    final waveHeight = 30.0;
    final waveLength = size.width / 4;

    // خط متموج علوي
    path.moveTo(0, size.height * 0.2);
    for (double x = 0; x <= size.width; x += 10) {
      final y = size.height * 0.2 +
                waveHeight * math.sin((x / waveLength + animationValue * 2) * 2 * math.pi);
      path.lineTo(x, y);
    }
    canvas.drawPath(path, paint);

    // خط متموج سفلي
    final path2 = Path();
    path2.moveTo(0, size.height * 0.8);
    for (double x = 0; x <= size.width; x += 10) {
      final y = size.height * 0.8 +
                waveHeight * math.sin((x / waveLength - animationValue * 2) * 2 * math.pi);
      path2.lineTo(x, y);
    }
    canvas.drawPath(path2, paint);
  }

  void _drawTwinklingDots(Canvas canvas, Size size, Paint paint) {
    paint.style = PaintingStyle.fill;

    // نقاط متلألئة
    for (int i = 0; i < 20; i++) {
      final x = size.width * (i % 5) / 5 + (size.width * 0.1);
      final y = size.height * (i ~/ 5) / 4 + (size.height * 0.1);

      final opacity = (math.sin(animationValue * 4 * math.pi + i) + 1) / 2;
      paint.color = Colors.white.withOpacity(opacity * 0.1);

      canvas.drawCircle(Offset(x, y), 3, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
