import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:math' as math;
import 'dart:async';

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_dimensions.dart';
import '../../core/utils/language_manager.dart';
import '../../core/services/driver_location_service.dart';
import '../../shared/widgets/company_bottom_navigation.dart';

class OrderManagementScreen extends StatefulWidget {
  const OrderManagementScreen({super.key});

  @override
  State<OrderManagementScreen> createState() => _OrderManagementScreenState();
}

class _OrderManagementScreenState extends State<OrderManagementScreen>
    with TickerProviderStateMixin {

  // ═══════════════════════════════════════════════════════════════
  // متحكمات الأنيميشن (Animation Controllers)
  // ═══════════════════════════════════════════════════════════════

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _backgroundController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _backgroundAnimation;

  // Timer للحذف التلقائي
  Timer? _autoDeleteTimer;

  // ═══════════════════════════════════════════════════════════════
  // متغيرات الحالة (State Variables)
  // ═══════════════════════════════════════════════════════════════

  String _selectedFilter = 'الكل'; // الفلتر المحدد
  String _searchQuery = ''; // نص البحث
  final TextEditingController _searchController = TextEditingController();

  // قوائم الفلاتر
  final List<String> _filterOptions = [
    'الكل',
    'جديد',
    'قيد التنفيذ',
    'مكتمل',
    'ملغي',
  ];

  // بيانات وهمية للطلبات
  final List<Map<String, dynamic>> _orders = [
    {
      'id': 'ORD-001',
      'cargoType': 'بضائع عامة',
      'weight': '500 كغ',
      'from': 'بغداد - الكرادة',
      'to': 'البصرة - الجبيلة',
      'status': 'جديد',
      'priority': 'عادي',
      'price': '75000 د.ع',
      'date': '2024/01/15',
      'time': '10:30',
      'driver': null,
      'customer': 'أحمد محمد علي',
      'phone': '07701234567',
      'deliveryContact': 'سعد حسين الجبوري',
      'deliveryPhone': '07801234568',
    },
    {
      'id': 'ORD-002',
      'cargoType': 'مواد غذائية',
      'weight': '200 كغ',
      'from': 'بغداد - الجادرية',
      'to': 'أربيل - عنكاوا',
      'status': 'قيد التنفيذ',
      'priority': 'عاجل',
      'price': '120000 د.ع',
      'date': '2024/01/14',
      'time': '08:15',
      'driver': 'محمد أحمد حسن',
      'customer': 'فاطمة علي حسين',
      'phone': '07801234567',
      'deliveryContact': 'أحمد محمود الخالدي',
      'deliveryPhone': '07501234569',
      'description': 'مواد غذائية مبردة تحتاج للتسليم السريع - تشمل خضروات ولحوم طازجة',
    },
    {
      'id': 'ORD-003',
      'cargoType': 'أجهزة إلكترونية',
      'weight': '50 كغ',
      'from': 'بغداد - المنصور',
      'to': 'النجف - الكوفة',
      'status': 'مكتمل',
      'priority': 'عاجل جداً',
      'price': '95000 د.ع',
      'date': '2024/01/13',
      'time': '14:45',
      'driver': 'علي حسن محمد',
      'customer': 'سارة أحمد علي',
      'phone': '07901234567',
      'deliveryContact': 'محمد علي الكوفي',
      'deliveryPhone': '07601234570',
      'deliveryDate': '2024/01/13',
    },
    {
      'id': 'ORD-004',
      'cargoType': 'أثاث ومنزلية',
      'weight': '800 كغ',
      'from': 'بغداد - الدورة',
      'to': 'كربلاء - الحر',
      'status': 'ملغي',
      'priority': 'عادي',
      'price': '60000 د.ع',
      'date': '2024/01/12',
      'time': '16:20',
      'driver': null,
      'customer': 'حسام محمد علي',
      'phone': '07601234567',
      'deliveryContact': 'فاطمة حسن الحر',
      'deliveryPhone': '07701234571',
      'cancelledDate': DateTime.now().subtract(const Duration(days: 3)).toIso8601String(),
      'autoDeleteDate': DateTime.now().add(const Duration(days: 4)).toIso8601String(),
      'cancellationReason': 'طلب العميل إلغاء الطلب لتغيير الموعد',
    },
    {
      'id': 'ORD-005',
      'cargoType': 'مواد بناء',
      'weight': '1200 كغ',
      'from': 'بغداد - الشعلة',
      'to': 'الموصل - الجامعة',
      'status': 'جديد',
      'priority': 'عاجل',
      'price': '150000 د.ع',
      'date': '2024/01/15',
      'time': '09:00',
      'driver': null,
      'customer': 'نور الدين أحمد',
      'phone': '07501234567',
      'deliveryContact': 'عمر يوسف الموصلي',
      'deliveryPhone': '07801234572',
    },
    {
      'id': 'ORD-006',
      'cargoType': 'مواد طبية',
      'weight': '150 كغ',
      'from': 'بغداد - الكاظمية',
      'to': 'كركوك - الواسطي',
      'status': 'ملغي',
      'priority': 'عاجل جداً',
      'price': '85000 د.ع',
      'date': '2024/01/10',
      'time': '11:30',
      'driver': null,
      'customer': 'ليلى أحمد صالح',
      'phone': '07901234573',
      'deliveryContact': 'حسن علي الكركوكي',
      'deliveryPhone': '07501234574',
      'cancelledDate': DateTime.now().subtract(const Duration(days: 6)).toIso8601String(),
      'autoDeleteDate': DateTime.now().add(const Duration(days: 1)).toIso8601String(),
      'cancellationReason': 'عدم توفر السائق المناسب للنقل الطبي',
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _startAutoDeleteTimer();
  }

  void _initializeAnimations() {
    // أنيميشن الظهور التدريجي
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // أنيميشن الانزلاق
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // أنيميشن التكبير
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // أنيميشن الخلفية المتحركة
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 30),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.linear,
    ));
  }

  void _startAnimations() {
    _fadeController.forward();
    _backgroundController.repeat();

    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _slideController.forward();
      }
    });

    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) {
        _scaleController.forward();
      }
    });
  }

  void _startAutoDeleteTimer() {
    // تشغيل Timer كل دقيقة للتحقق من الطلبات المنتهية الصلاحية
    _autoDeleteTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkAndDeleteExpiredOrders();
    });
  }

  void _checkAndDeleteExpiredOrders() {
    final now = DateTime.now();
    final expiredOrders = <Map<String, dynamic>>[];

    for (final order in _orders) {
      if (order['status'] == 'ملغي' && order['autoDeleteDate'] != null) {
        final deleteDate = DateTime.parse(order['autoDeleteDate']);
        if (now.isAfter(deleteDate)) {
          expiredOrders.add(order);
        }
      }
    }

    if (expiredOrders.isNotEmpty) {
      setState(() {
        for (final order in expiredOrders) {
          _orders.remove(order);
        }
      });

      // عرض رسالة للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Consumer<LanguageManager>(
              builder: (context, langManager, child) {
                return Text(
                  langManager.getText(
                    'تم حذف ${expiredOrders.length} طلب منتهي الصلاحية تلقائياً',
                    '${expiredOrders.length} expired orders deleted automatically'
                  ),
                  style: GoogleFonts.tajawal(),
                );
              },
            ),
            backgroundColor: AppColors.warning,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _backgroundController.dispose();
    _searchController.dispose();
    _autoDeleteTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
        animation: _backgroundAnimation,
        builder: (context, child) {
          return Stack(
            children: [
              // الخلفية المتحركة
              _buildAnimatedBackground(),

              // المحتوى الرئيسي
              SafeArea(
                child: Column(
                  children: [
                    // شريط التطبيق المخصص
                    _buildCustomAppBar(),

                    // شريط البحث والفلاتر
                    _buildSearchAndFilters(),

                    // قائمة الطلبات
                    Expanded(
                      child: _buildOrdersList(),
                    ),
                  ],
                ),
              ),

              // زر إضافة طلب جديد
              _buildFloatingActionButton(),
            ],
          );
        },
      ),
      bottomNavigationBar: CompanyBottomNavigation(
        currentIndex: 1, // الطلبات
        onTap: (index) => CompanyBottomNavigation.handleNavigation(context, index),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Positioned.fill(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1E3A8A), // أزرق داكن في الأعلى
              Color(0xFF3B82F6), // أزرق متوسط
              Color(0xFF60A5FA), // أزرق فاتح
              Color(0xFF93C5FD), // أزرق فاتح جداً
              Color(0xFFFBBF24), // برتقالي ذهبي في الأسفل
            ],
            stops: [0.0, 0.3, 0.6, 0.8, 1.0],
          ),
        ),
        child: CustomPaint(
          painter: OrderManagementBackgroundPainter(_backgroundAnimation.value),
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return Row(
              children: [
                // زر الرجوع
                Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => Navigator.pop(context),
                      borderRadius: BorderRadius.circular(12),
                      child: const Icon(
                        Bootstrap.arrow_left,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // العنوان
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        langManager.getText('إدارة الطلبات', 'Order Management'),
                        style: GoogleFonts.tajawal(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        langManager.getText('متابعة وإدارة جميع الطلبات', 'Track and manage all orders'),
                        style: GoogleFonts.tajawal(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),

                // أيقونة الإشعارات
                Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: _showNotifications,
                      borderRadius: BorderRadius.circular(12),
                      child: Stack(
                        children: [
                          const Center(
                            child: Icon(
                              Bootstrap.bell,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          // نقطة الإشعار
                          Positioned(
                            top: 8,
                            right: 8,
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return Column(
              children: [
                // شريط البحث
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.95),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.8),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.15),
                        blurRadius: 15,
                        offset: const Offset(0, 8),
                        spreadRadius: 2,
                      ),
                      BoxShadow(
                        color: Colors.white.withOpacity(0.9),
                        blurRadius: 8,
                        offset: const Offset(0, -2),
                        spreadRadius: -2,
                      ),
                    ],
                  ),
                  child: SizedBox(
                    height: AppDimensions.searchFieldHeight,
                    child: TextField(
                      controller: _searchController,
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                      style: GoogleFonts.tajawal(
                        color: Colors.black87,
                        fontSize: AppDimensions.fontSizeM,
                        fontWeight: FontWeight.w500,
                      ),
                      decoration: InputDecoration(
                        hintText: langManager.getText('البحث في الطلبات...', 'Search orders...'),
                        hintStyle: GoogleFonts.tajawal(
                          color: Colors.grey.withOpacity(0.7),
                          fontSize: AppDimensions.fontSizeM,
                        ),
                        prefixIcon: Container(
                          margin: const EdgeInsets.all(8),
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                          ),
                          child: Icon(
                            Bootstrap.search,
                            color: AppColors.primary,
                            size: AppDimensions.iconSizeS,
                          ),
                        ),
                        suffixIcon: _searchQuery.isNotEmpty
                            ? IconButton(
                                onPressed: () {
                                  _searchController.clear();
                                  setState(() {
                                    _searchQuery = '';
                                  });
                                },
                                icon: Icon(
                                  Bootstrap.x_circle,
                                  color: Colors.grey.withOpacity(0.7),
                                  size: AppDimensions.iconSizeS,
                                ),
                              )
                            : null,
                        border: InputBorder.none,
                        contentPadding: AppSpacing.inputPadding,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // فلاتر الحالة
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _filterOptions.length,
                    itemBuilder: (context, index) {
                      final filter = _filterOptions[index];
                      final isSelected = _selectedFilter == filter;

                      return Container(
                        margin: EdgeInsets.only(
                          right: index < _filterOptions.length - 1 ? 12 : 0,
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                _selectedFilter = filter;
                              });
                            },
                            borderRadius: BorderRadius.circular(20),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                gradient: isSelected
                                    ? const LinearGradient(
                                        colors: [
                                          Color(0xFF1F2937),
                                          Color(0xFF374151),
                                        ],
                                      )
                                    : null,
                                color: isSelected ? null : const Color(0xFF1F2937).withOpacity(0.8),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: isSelected
                                      ? Colors.white.withOpacity(0.3)
                                      : Colors.white.withOpacity(0.2),
                                  width: 1,
                                ),
                                boxShadow: isSelected ? [
                                  BoxShadow(
                                    color: const Color(0xFF1F2937).withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ] : null,
                              ),
                              child: Text(
                                filter,
                                style: GoogleFonts.tajawal(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }



  Widget _buildOrdersList() {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            // فلترة الطلبات
            List<Map<String, dynamic>> filteredOrders = _orders.where((order) {
              // فلترة حسب الحالة
              bool statusMatch = _selectedFilter == 'الكل' || order['status'] == _selectedFilter;

              // فلترة حسب البحث
              bool searchMatch = _searchQuery.isEmpty ||
                  order['id'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
                  order['cargoType'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
                  order['from'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
                  order['to'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
                  order['customer'].toString().toLowerCase().contains(_searchQuery.toLowerCase());

              return statusMatch && searchMatch;
            }).toList();

            if (filteredOrders.isEmpty) {
              return _buildEmptyState(langManager);
            }

            return ListView.builder(
              padding: const EdgeInsets.only(bottom: 100),
              itemCount: filteredOrders.length,
              itemBuilder: (context, index) {
                final order = filteredOrders[index];
                return _buildOrderCard(order, langManager);
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState(LanguageManager langManager) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Bootstrap.inbox,
              color: Colors.white.withOpacity(0.7),
              size: 60,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            langManager.getText('لا توجد طلبات', 'No orders found'),
            style: GoogleFonts.tajawal(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            langManager.getText('جرب تغيير الفلتر أو البحث', 'Try changing filter or search'),
            style: GoogleFonts.tajawal(
              color: Colors.white.withOpacity(0.7),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(Map<String, dynamic> order, LanguageManager langManager) {
    final bool isInProgress = order['status'] == 'قيد التنفيذ';
    final bool isCancelled = order['status'] == 'ملغي';
    final bool isCompleted = order['status'] == 'مكتمل';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1F2937).withOpacity(0.95),
            const Color(0xFF374151).withOpacity(0.9),
            _getStatusColor(order['status']).withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(order['status']).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: _getStatusColor(order['status']).withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showOrderDetails(order, langManager),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: EdgeInsets.all((isInProgress || isCancelled) ? 12 : 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس الكارت
                Row(
                  children: [
                    // رقم الطلب
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: _getStatusColor(order['status']).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        order['id'],
                        style: GoogleFonts.tajawal(
                          color: _getStatusColor(order['status']),
                          fontSize: (isInProgress || isCancelled) ? 12 : 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    const Spacer(),

                    // حالة الطلب
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: _getStatusColor(order['status']),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        order['status'],
                        style: GoogleFonts.tajawal(
                          color: Colors.white,
                          fontSize: (isInProgress || isCancelled) ? 10 : 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: (isInProgress || isCancelled) ? 8 : 12),

                // تفاصيل الطلب - مختلفة حسب الحالة
                if (isInProgress) ...[
                  // للطلبات قيد التنفيذ - عرض بيانات المستلم والوصف
                  _buildOrderDetailRow(
                    icon: Bootstrap.person,
                    label: langManager.getText('المستلم', 'Receiver'),
                    value: order['deliveryContact'] ?? order['customer'],
                    color: AppColors.primary,
                  ),
                  const SizedBox(height: 6),
                  _buildOrderDetailRow(
                    icon: Bootstrap.file_text,
                    label: langManager.getText('الوصف', 'Description'),
                    value: order['description'] ?? order['cargoType'],
                    color: AppColors.info,
                  ),
                ] else if (isCancelled) ...[
                  // للطلبات الملغية - عرض سبب الإلغاء والتاريخ
                  _buildOrderDetailRow(
                    icon: Bootstrap.exclamation_triangle,
                    label: langManager.getText('سبب الإلغاء', 'Cancellation Reason'),
                    value: order['cancellationReason'] ?? langManager.getText('لم يتم تحديد السبب', 'No reason specified'),
                    color: Colors.red,
                  ),
                  const SizedBox(height: 6),
                  _buildOrderDetailRow(
                    icon: Bootstrap.calendar_x,
                    label: langManager.getText('تاريخ الإلغاء', 'Cancelled Date'),
                    value: order['cancelledDate'] != null
                        ? DateTime.parse(order['cancelledDate']).toString().split(' ')[0]
                        : langManager.getText('غير محدد', 'Not specified'),
                    color: Colors.orange,
                  ),
                ] else if (isCompleted) ...[
                  // للطلبات المكتملة - عرض تاريخ التسليم والسائق
                  _buildOrderDetailRow(
                    icon: Bootstrap.check_circle,
                    label: langManager.getText('تاريخ التسليم', 'Delivery Date'),
                    value: order['deliveryDate'] ?? order['date'],
                    color: AppColors.success,
                  ),
                  const SizedBox(height: 6),
                  _buildOrderDetailRow(
                    icon: Bootstrap.person_check,
                    label: langManager.getText('السائق المنفذ', 'Delivery Driver'),
                    value: order['driver'] ?? langManager.getText('غير محدد', 'Not specified'),
                    color: AppColors.primary,
                  ),
                ] else ...[
                  // للطلبات الأخرى - عرض التفاصيل العادية
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildOrderDetailRow(
                              icon: Bootstrap.box,
                              label: langManager.getText('النوع', 'Type'),
                              value: order['cargoType'],
                              color: AppColors.primary,
                            ),
                            const SizedBox(height: 6),
                            _buildOrderDetailRow(
                              icon: Bootstrap.speedometer,
                              label: langManager.getText('الوزن', 'Weight'),
                              value: order['weight'],
                              color: AppColors.info,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildOrderDetailRow(
                              icon: Bootstrap.currency_dollar,
                              label: langManager.getText('السعر', 'Price'),
                              value: order['price'],
                              color: AppColors.success,
                            ),
                            const SizedBox(height: 6),
                            _buildOrderDetailRow(
                              icon: Bootstrap.exclamation_circle,
                              label: langManager.getText('الأولوية', 'Priority'),
                              value: order['priority'],
                              color: _getPriorityColor(order['priority']),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: 12),

                // المسار
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: AppColors.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Bootstrap.geo_alt,
                              color: AppColors.primary,
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  langManager.getText('من', 'From'),
                                  style: GoogleFonts.tajawal(
                                    fontSize: 12,
                                    color: Colors.grey[300],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  order['from'],
                                  style: GoogleFonts.tajawal(
                                    fontSize: 14,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 10),

                      Row(
                        children: [
                          Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: AppColors.success.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Bootstrap.pin_map,
                              color: AppColors.success,
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  langManager.getText('إلى', 'To'),
                                  style: GoogleFonts.tajawal(
                                    fontSize: 12,
                                    color: Colors.grey[300],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  order['to'],
                                  style: GoogleFonts.tajawal(
                                    fontSize: 14,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 12),

                // معلومات إضافية
                Row(
                  children: [
                    Icon(
                      Bootstrap.calendar,
                      color: Colors.grey[300],
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${order['date']} - ${order['time']}',
                      style: GoogleFonts.tajawal(
                        fontSize: 12,
                        color: Colors.grey[300],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    // للطلبات الملغية - عرض الموقت التنازلي
                    if (order['status'] == 'ملغي') ...[
                      _buildCountdownTimer(order, langManager),
                    ]
                    // عرض اسم السائق فقط للطلبات قيد التنفيذ
                    else if (isInProgress && order['driver'] != null) ...[
                      GestureDetector(
                        onTap: () => _showOrderDetails(order, langManager),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppColors.success.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: AppColors.success.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Bootstrap.person_check,
                                color: AppColors.success,
                                size: 14,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                order['driver'],
                                style: GoogleFonts.tajawal(
                                  fontSize: 11,
                                  color: AppColors.success,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ] else if (!isInProgress && !isCancelled && !isCompleted) ...[
                      // للطلبات الجديدة - عرض حالة السائق العادية
                      if (order['driver'] != null) ...[
                        Icon(
                          Bootstrap.person_check,
                          color: AppColors.success,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          order['driver'],
                          style: GoogleFonts.tajawal(
                            fontSize: 12,
                            color: AppColors.success,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ] else ...[
                        Icon(
                          Bootstrap.person_x,
                          color: Colors.grey[300],
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          langManager.getText('لا يوجد سائق', 'No driver'),
                          style: GoogleFonts.tajawal(
                            fontSize: 12,
                            color: Colors.grey[300],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ],
                  ],
                ),

                // زر إعادة التنفيذ للطلبات الملغية
                if (order['status'] == 'ملغي') ...[
                  const SizedBox(height: 12),
                  _buildReactivateButton(order, langManager),
                ]
                // زر إرسال مخصص للطلبات الجديدة
                else if (order['status'] == 'جديد') ...[
                  const SizedBox(height: 12),
                  _buildCustomSendButton(order, langManager),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOrderDetailRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: color,
          size: 16,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.tajawal(
                  fontSize: 12,
                  color: Colors.grey[300],
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: GoogleFonts.tajawal(
                  fontSize: 14,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFloatingActionButton() {
    return Positioned(
      bottom: 30,
      right: 20,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return Container(
              height: 56,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.primary,
                    Color(0xFF2563EB),
                  ],
                ),
                borderRadius: BorderRadius.circular(28),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.4),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                    spreadRadius: 2,
                  ),
                  BoxShadow(
                    color: Colors.white.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                    spreadRadius: -1,
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: _createNewOrder,
                  borderRadius: BorderRadius.circular(28),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Icon(
                          Bootstrap.plus_lg,
                          color: Colors.white,
                          size: 18,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        langManager.getText('طلب جديد', 'New Order'),
                        style: GoogleFonts.tajawal(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال مساعدة (Helper Functions)
  // ═══════════════════════════════════════════════════════════════

  Color _getStatusColor(String status) {
    switch (status) {
      case 'جديد':
        return AppColors.warning;
      case 'قيد التنفيذ':
        return AppColors.info;
      case 'مكتمل':
        return AppColors.success;
      case 'ملغي':
        return Colors.red;
      default:
        return AppColors.primary;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'عادي':
        return AppColors.primary;
      case 'عاجل':
        return AppColors.warning;
      case 'عاجل جداً':
        return Colors.red;
      default:
        return AppColors.primary;
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال الإجراءات (Action Functions)
  // ═══════════════════════════════════════════════════════════════

  void _showNotifications() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          Provider.of<LanguageManager>(context, listen: false)
              .getText('شاشة الإشعارات قريباً', 'Notifications screen coming soon'),
          style: GoogleFonts.tajawal(),
        ),
        backgroundColor: AppColors.info,
      ),
    );
  }

  Widget _buildCountdownTimer(Map<String, dynamic> order, LanguageManager langManager) {
    if (order['autoDeleteDate'] == null) return const SizedBox.shrink();

    final deleteDate = DateTime.parse(order['autoDeleteDate']);
    final now = DateTime.now();
    final difference = deleteDate.difference(now);

    if (difference.isNegative) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Text(
          langManager.getText('منتهي الصلاحية', 'Expired'),
          style: GoogleFonts.tajawal(
            fontSize: 10,
            color: Colors.red,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }

    final days = difference.inDays;
    final hours = difference.inHours % 24;
    final minutes = difference.inMinutes % 60;

    String timeText;
    if (days > 0) {
      timeText = langManager.getText('$days يوم', '$days days');
    } else if (hours > 0) {
      timeText = langManager.getText('$hours ساعة', '$hours hours');
    } else {
      timeText = langManager.getText('$minutes دقيقة', '$minutes min');
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Bootstrap.clock,
            color: Colors.orange,
            size: 12,
          ),
          const SizedBox(width: 4),
          Text(
            timeText,
            style: GoogleFonts.tajawal(
              fontSize: 10,
              color: Colors.orange,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReactivateButton(Map<String, dynamic> order, LanguageManager langManager) {
    return Container(
      width: double.infinity,
      height: 36,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _reactivateOrder(order, langManager),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.success.withOpacity(0.8),
                  AppColors.success,
                ],
              ),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: AppColors.success.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Bootstrap.arrow_clockwise,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  langManager.getText('إعادة التنفيذ', 'Reactivate'),
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCustomSendButton(Map<String, dynamic> order, LanguageManager langManager) {
    return Container(
      width: double.infinity,
      height: 36,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showCustomSendOptions(order, langManager),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withOpacity(0.8),
                  AppColors.primary,
                ],
              ),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Bootstrap.send,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  langManager.getText('إرسال مخصص', 'Custom Send'),
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showCustomSendOptions(Map<String, dynamic> order, LanguageManager langManager) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.black.withOpacity(0.9),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.white.withOpacity(0.2)),
          ),
          title: Row(
            children: [
              Icon(
                Bootstrap.send,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                langManager.getText('إرسال مخصص', 'Custom Send'),
                style: GoogleFonts.tajawal(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // الخيار الأول: إرسال للسائقين القريبين
              Container(
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFF374151),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.1)),
                ),
                child: ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Bootstrap.geo_alt, color: AppColors.primary, size: 20),
                  ),
                  title: Text(
                    langManager.getText('إرسال للسائقين القريبين', 'Send to Nearby Drivers'),
                    style: GoogleFonts.tajawal(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  subtitle: Text(
                    langManager.getText('إرسال الطلب للسائقين في نطاق 5 كم', 'Send order to drivers within 5km radius'),
                    style: GoogleFonts.tajawal(
                      color: Colors.grey[400],
                      fontSize: 12,
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _sendToNearbyDrivers(order, langManager);
                  },
                ),
              ),

              // الخيار الثاني: إرسال حسب نوع المركبة
              Container(
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFF374151),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.1)),
                ),
                child: ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.success.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Bootstrap.truck, color: AppColors.success, size: 20),
                  ),
                  title: Text(
                    langManager.getText('إرسال حسب نوع المركبة', 'Send by Vehicle Type'),
                    style: GoogleFonts.tajawal(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  subtitle: Text(
                    langManager.getText('إرسال للسائقين الذين يملكون نوع المركبة المطلوبة', 'Send to drivers with required vehicle type'),
                    style: GoogleFonts.tajawal(
                      color: Colors.grey[400],
                      fontSize: 12,
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _sendByVehicleType(order, langManager);
                  },
                ),
              ),

              // الخيار الثالث: إرسال للسائقين المميزين
              Container(
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFF374151),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.1)),
                ),
                child: ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.amber.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Bootstrap.star, color: Colors.amber, size: 20),
                  ),
                  title: Text(
                    langManager.getText('إرسال للسائقين المميزين', 'Send to Top Rated Drivers'),
                    style: GoogleFonts.tajawal(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  subtitle: Text(
                    langManager.getText('إرسال للسائقين ذوي التقييم العالي (4.5+)', 'Send to highly rated drivers (4.5+)'),
                    style: GoogleFonts.tajawal(
                      color: Colors.grey[400],
                      fontSize: 12,
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _sendToTopRatedDrivers(order, langManager);
                  },
                ),
              ),

              // الخيار الرابع: عرض السائقين المتوفرين
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF374151),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.1)),
                ),
                child: ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.info.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Bootstrap.people, color: AppColors.info, size: 20),
                  ),
                  title: Text(
                    langManager.getText('عرض السائقين المتوفرين', 'View Available Drivers'),
                    style: GoogleFonts.tajawal(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  subtitle: Text(
                    langManager.getText('عرض قائمة السائقين واختيار سائق محدد', 'View drivers list and select specific driver'),
                    style: GoogleFonts.tajawal(
                      color: Colors.grey[400],
                      fontSize: 12,
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showAvailableDrivers(order, langManager);
                  },
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                langManager.getText('إلغاء', 'Cancel'),
                style: GoogleFonts.tajawal(
                  color: Colors.white.withOpacity(0.7),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _sendToNearbyDrivers(Map<String, dynamic> order, LanguageManager langManager) {
    // عرض نافذة تأكيد مع تفاصيل السائقين القريبين
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1F2937),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.white.withOpacity(0.2)),
          ),
          title: Row(
            children: [
              Icon(Bootstrap.geo_alt, color: AppColors.primary, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  langManager.getText('إرسال للسائقين القريبين', 'Send to Nearby Drivers'),
                  style: GoogleFonts.tajawal(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                langManager.getText('سيتم إرسال الطلب إلى:', 'Order will be sent to:'),
                style: GoogleFonts.tajawal(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              _buildDriverInfo('أحمد محمد علي', '2.5 كم', 4.8),
              _buildDriverInfo('محمد حسن أحمد', '3.2 كم', 4.6),
              _buildDriverInfo('علي حسين محمد', '4.1 كم', 4.9),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.primary.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Bootstrap.info_circle, color: AppColors.primary, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        langManager.getText('سيتم إشعار 3 سائقين في نطاق 5 كم', '3 drivers within 5km will be notified'),
                        style: GoogleFonts.tajawal(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                langManager.getText('إلغاء', 'Cancel'),
                style: GoogleFonts.tajawal(
                  color: Colors.white.withOpacity(0.7),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  order['status'] = 'تم الإرسال للقريبين';
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      langManager.getText('تم إرسال الطلب لـ 3 سائقين قريبين', 'Order sent to 3 nearby drivers'),
                      style: GoogleFonts.tajawal(),
                    ),
                    backgroundColor: AppColors.primary,
                    duration: const Duration(seconds: 3),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              child: Text(
                langManager.getText('إرسال الآن', 'Send Now'),
                style: GoogleFonts.tajawal(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDriverInfo(String name, String distance, double rating) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Bootstrap.person, color: AppColors.primary, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              name,
              style: GoogleFonts.tajawal(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Bootstrap.star_fill, color: Colors.amber, size: 12),
              const SizedBox(width: 2),
              Text(
                rating.toString(),
                style: GoogleFonts.tajawal(
                  color: Colors.grey[300],
                  fontSize: 11,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                distance,
                style: GoogleFonts.tajawal(
                  color: Colors.grey[400],
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _sendByVehicleType(Map<String, dynamic> order, LanguageManager langManager) {
    final vehicleType = order['vehicleType'] ?? 'أتيكو 6 متر';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1F2937),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.white.withOpacity(0.2)),
          ),
          title: Row(
            children: [
              Icon(Bootstrap.truck, color: AppColors.success, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  langManager.getText('إرسال حسب نوع المركبة', 'Send by Vehicle Type'),
                  style: GoogleFonts.tajawal(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.success.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.success.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Bootstrap.truck, color: AppColors.success, size: 20),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            langManager.getText('نوع المركبة المطلوبة:', 'Required Vehicle Type:'),
                            style: GoogleFonts.tajawal(
                              color: Colors.white.withOpacity(0.8),
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            vehicleType,
                            style: GoogleFonts.tajawal(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                langManager.getText('السائقين المتوفرين بهذا النوع:', 'Available drivers with this type:'),
                style: GoogleFonts.tajawal(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              _buildVehicleDriverInfo('أحمد محمد علي', vehicleType, 4.8),
              _buildVehicleDriverInfo('محمد حسن أحمد', vehicleType, 4.6),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.success.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.success.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Bootstrap.info_circle, color: AppColors.success, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        langManager.getText('سيتم إشعار 2 سائق يملكان هذا النوع', '2 drivers with this vehicle type will be notified'),
                        style: GoogleFonts.tajawal(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                langManager.getText('إلغاء', 'Cancel'),
                style: GoogleFonts.tajawal(
                  color: Colors.white.withOpacity(0.7),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  order['status'] = 'تم الإرسال حسب النوع';
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      langManager.getText('تم إرسال الطلب لسائقي $vehicleType', 'Order sent to $vehicleType drivers'),
                      style: GoogleFonts.tajawal(),
                    ),
                    backgroundColor: AppColors.success,
                    duration: const Duration(seconds: 3),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              child: Text(
                langManager.getText('إرسال الآن', 'Send Now'),
                style: GoogleFonts.tajawal(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildVehicleDriverInfo(String name, String vehicleType, double rating) {
    return Container(
      margin: const EdgeInsets.only(bottom: 6),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Bootstrap.person, color: AppColors.success, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: GoogleFonts.tajawal(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  vehicleType,
                  style: GoogleFonts.tajawal(
                    color: Colors.grey[400],
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Bootstrap.star_fill, color: Colors.amber, size: 12),
              const SizedBox(width: 2),
              Text(
                rating.toString(),
                style: GoogleFonts.tajawal(
                  color: Colors.grey[300],
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _sendToTopRatedDrivers(Map<String, dynamic> order, LanguageManager langManager) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1F2937),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.white.withOpacity(0.2)),
          ),
          title: Row(
            children: [
              Icon(Bootstrap.star, color: Colors.amber, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  langManager.getText('إرسال للسائقين المميزين', 'Send to Top Rated Drivers'),
                  style: GoogleFonts.tajawal(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.amber.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.amber.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Bootstrap.award, color: Colors.amber, size: 20),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        langManager.getText('السائقين ذوي التقييم العالي (4.5+)', 'High-rated drivers (4.5+)'),
                        style: GoogleFonts.tajawal(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                langManager.getText('السائقين المميزين المتوفرين:', 'Available top-rated drivers:'),
                style: GoogleFonts.tajawal(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              _buildTopDriverInfo('علي حسين محمد', 4.9, '150+ رحلة'),
              _buildTopDriverInfo('أحمد محمد علي', 4.8, '200+ رحلة'),
              _buildTopDriverInfo('محمد أحمد حسن', 4.7, '180+ رحلة'),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.amber.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.amber.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Bootstrap.info_circle, color: Colors.amber, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        langManager.getText('سيتم إشعار 3 سائقين مميزين فقط', 'Only 3 top-rated drivers will be notified'),
                        style: GoogleFonts.tajawal(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                langManager.getText('إلغاء', 'Cancel'),
                style: GoogleFonts.tajawal(
                  color: Colors.white.withOpacity(0.7),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  order['status'] = 'تم الإرسال للمميزين';
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      langManager.getText('تم إرسال الطلب لـ 3 سائقين مميزين', 'Order sent to 3 top-rated drivers'),
                      style: GoogleFonts.tajawal(),
                    ),
                    backgroundColor: Colors.amber,
                    duration: const Duration(seconds: 3),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              child: Text(
                langManager.getText('إرسال الآن', 'Send Now'),
                style: GoogleFonts.tajawal(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTopDriverInfo(String name, double rating, String trips) {
    return Container(
      margin: const EdgeInsets.only(bottom: 6),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.2),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(Bootstrap.person, color: Colors.amber, size: 16),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: GoogleFonts.tajawal(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  trips,
                  style: GoogleFonts.tajawal(
                    color: Colors.grey[400],
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Bootstrap.star_fill, color: Colors.amber, size: 12),
                const SizedBox(width: 4),
                Text(
                  rating.toString(),
                  style: GoogleFonts.tajawal(
                    color: Colors.amber,
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showAvailableDrivers(Map<String, dynamic> order, LanguageManager langManager) {
    // بيانات وهمية للسائقين المتوفرين
    final availableDrivers = [
      {
        'id': 'driver_1',
        'name': 'أحمد محمد علي',
        'rating': 4.8,
        'distance': '2.5 كم',
        'vehicleType': order['vehicleType'] ?? 'أتيكو 6 متر',
        'phone': '07701234567',
        'estimatedArrival': '15 دقيقة',
      },
      {
        'id': 'driver_2',
        'name': 'محمد حسن أحمد',
        'rating': 4.6,
        'distance': '3.2 كم',
        'vehicleType': order['vehicleType'] ?? 'أتيكو 6 متر',
        'phone': '07801234568',
        'estimatedArrival': '20 دقيقة',
      },
      {
        'id': 'driver_3',
        'name': 'علي حسين محمد',
        'rating': 4.9,
        'distance': '4.1 كم',
        'vehicleType': order['vehicleType'] ?? 'أتيكو 6 متر',
        'phone': '07901234569',
        'estimatedArrival': '25 دقيقة',
      },
    ];

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.7,
          decoration: BoxDecoration(
            color: const Color(0xFF1F2937),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // العنوان
              Row(
                children: [
                  Icon(Bootstrap.people, color: AppColors.primary, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      langManager.getText('السائقين المتوفرين', 'Available Drivers'),
                      style: GoogleFonts.tajawal(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(Bootstrap.x_lg, color: Colors.white),
                  ),
                ],
              ),
              Divider(color: Colors.white.withOpacity(0.2)),

              // قائمة السائقين
              Expanded(
                child: ListView.builder(
                  itemCount: availableDrivers.length,
                  itemBuilder: (context, index) {
                    final driver = availableDrivers[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF374151),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.white.withOpacity(0.1)),
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: AppColors.primary.withOpacity(0.2),
                          child: Icon(Bootstrap.person, color: AppColors.primary),
                        ),
                        title: Text(
                          driver['name'],
                          style: GoogleFonts.tajawal(
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Bootstrap.star_fill, color: Colors.amber, size: 14),
                                const SizedBox(width: 4),
                                Text(
                                  '${driver['rating']}',
                                  style: TextStyle(color: Colors.grey[300], fontSize: 12),
                                ),
                                const SizedBox(width: 12),
                                Icon(Bootstrap.geo_alt, color: Colors.grey[400], size: 14),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    driver['distance'],
                                    style: TextStyle(color: Colors.grey[300], fontSize: 12),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              langManager.getText('وقت الوصول: ${driver['estimatedArrival']}',
                                'Arrival: ${driver['estimatedArrival']}'),
                              style: TextStyle(color: Colors.grey[400], fontSize: 11),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                        trailing: SizedBox(
                          width: 60,
                          height: 32,
                          child: ElevatedButton(
                            onPressed: () => _sendOrderToDriver(order, driver, langManager),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.success,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
                              padding: const EdgeInsets.symmetric(horizontal: 8),
                            ),
                            child: Text(
                              langManager.getText('إرسال', 'Send'),
                              style: GoogleFonts.tajawal(color: Colors.white, fontSize: 11),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _sendOrderToDriver(Map<String, dynamic> order, Map<String, dynamic> driver, LanguageManager langManager) {
    Navigator.pop(context); // إغلاق نافذة السائقين

    setState(() {
      order['status'] = 'تم الإرسال';
      order['driver'] = driver['name'];
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          langManager.getText('تم إرسال الطلب إلى ${driver['name']}', 'Order sent to ${driver['name']}'),
          style: GoogleFonts.tajawal(),
        ),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _reactivateOrder(Map<String, dynamic> order, LanguageManager langManager) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.black.withOpacity(0.9),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.white.withOpacity(0.2)),
          ),
          title: Row(
            children: [
              Icon(
                Bootstrap.arrow_clockwise,
                color: AppColors.success,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                langManager.getText('إعادة تنفيذ الطلب', 'Reactivate Order'),
                style: GoogleFonts.tajawal(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Text(
            langManager.getText(
              'اختر طريقة إعادة تنفيذ الطلب ${order['id']}:',
              'Choose how to reactivate order ${order['id']}:'
            ),
            style: GoogleFonts.tajawal(
              color: Colors.white.withOpacity(0.8),
              fontSize: 14,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                langManager.getText('إلغاء', 'Cancel'),
                style: GoogleFonts.tajawal(
                  color: Colors.white.withOpacity(0.7),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _editAndReactivateOrder(order, langManager);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                langManager.getText('تعديل وإعادة تنفيذ', 'Edit & Reactivate'),
                style: GoogleFonts.tajawal(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  order['status'] = 'جديد';
                  order.remove('cancelledDate');
                  order.remove('autoDeleteDate');
                });
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      langManager.getText('تم إعادة تنفيذ الطلب بنجاح', 'Order reactivated successfully'),
                      style: GoogleFonts.tajawal(),
                    ),
                    backgroundColor: AppColors.success,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                langManager.getText('إعادة تنفيذ مباشرة', 'Reactivate Directly'),
                style: GoogleFonts.tajawal(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _editAndReactivateOrder(Map<String, dynamic> order, LanguageManager langManager) async {
    final result = await Navigator.pushNamed(
      context,
      '/create-order',
      arguments: order, // تمرير بيانات الطلب للتعديل
    );

    if (result != null && result is Map<String, dynamic>) {
      setState(() {
        // تحديث الطلب الحالي بالبيانات الجديدة
        final index = _orders.indexWhere((o) => o['id'] == order['id']);
        if (index != -1) {
          _orders[index] = {
            ...result,
            'id': order['id'], // الحفاظ على نفس المعرف
            'status': 'جديد', // تغيير الحالة إلى جديد
          };
          // حذف بيانات الإلغاء
          _orders[index].remove('cancelledDate');
          _orders[index].remove('autoDeleteDate');
        }
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            langManager.getText('تم تعديل وإعادة تنفيذ الطلب بنجاح', 'Order edited and reactivated successfully'),
            style: GoogleFonts.tajawal(),
          ),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }

  void _createNewOrder() async {
    final result = await Navigator.pushNamed(context, '/create-order');

    // إذا تم إرجاع طلب جديد من شاشة إنشاء الطلب
    if (result != null && result is Map<String, dynamic>) {
      setState(() {
        // إضافة الطلب الجديد في بداية القائمة
        _orders.insert(0, result);
        // تغيير الفلتر إلى "جديد" لعرض الطلب الجديد
        _selectedFilter = 'جديد';
      });

      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Consumer<LanguageManager>(
              builder: (context, langManager, child) {
                return Text(
                  langManager.getText('تم إضافة الطلب بنجاح', 'Order added successfully'),
                  style: GoogleFonts.tajawal(),
                );
              },
            ),
            backgroundColor: AppColors.success,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showOrderDetails(Map<String, dynamic> order, LanguageManager langManager) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildOrderDetailsSheet(order, langManager),
    );
  }

  Widget _buildOrderDetailsSheet(Map<String, dynamic> order, LanguageManager langManager) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF1F2937),
            Color(0xFF374151),
            Color(0xFF4B5563),
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // مقبض السحب
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.5),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // رأس الشيت
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Text(
                  langManager.getText('تفاصيل الطلب', 'Order Details'),
                  style: GoogleFonts.tajawal(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(order['status']),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    order['status'],
                    style: GoogleFonts.tajawal(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التفاصيل
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات أساسية
                  _buildDetailSection(
                    title: langManager.getText('معلومات أساسية', 'Basic Information'),
                    children: [
                      _buildDetailItem(
                        label: langManager.getText('رقم الطلب', 'Order ID'),
                        value: order['id'],
                        icon: Bootstrap.hash,
                      ),
                      _buildDetailItem(
                        label: langManager.getText('نوع البضاعة', 'Cargo Type'),
                        value: order['cargoType'],
                        icon: Bootstrap.box,
                      ),
                      _buildDetailItem(
                        label: langManager.getText('الوزن', 'Weight'),
                        value: order['weight'],
                        icon: Bootstrap.speedometer,
                      ),
                      _buildDetailItem(
                        label: langManager.getText('السعر', 'Price'),
                        value: order['price'],
                        icon: Bootstrap.currency_dollar,
                      ),
                      _buildDetailItem(
                        label: langManager.getText('الأولوية', 'Priority'),
                        value: order['priority'],
                        icon: Bootstrap.exclamation_circle,
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // معلومات المرسل
                  _buildDetailSection(
                    title: langManager.getText('معلومات المرسل', 'Sender Information'),
                    children: [
                      _buildDetailItem(
                        label: langManager.getText('اسم المرسل', 'Sender Name'),
                        value: order['customer'],
                        icon: Bootstrap.person,
                      ),
                      _buildDetailItem(
                        label: langManager.getText('رقم هاتف المرسل', 'Sender Phone'),
                        value: order['phone'],
                        icon: Bootstrap.telephone,
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // معلومات المستلم
                  _buildDetailSection(
                    title: langManager.getText('معلومات المستلم', 'Receiver Information'),
                    children: [
                      _buildDetailItem(
                        label: langManager.getText('اسم المستلم', 'Receiver Name'),
                        value: order['deliveryContact'] ?? langManager.getText('غير محدد', 'Not specified'),
                        icon: Bootstrap.person_check,
                      ),
                      _buildDetailItem(
                        label: langManager.getText('رقم هاتف المستلم', 'Receiver Phone'),
                        value: order['deliveryPhone'] ?? langManager.getText('غير محدد', 'Not specified'),
                        icon: Bootstrap.telephone_fill,
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // معلومات المسار
                  _buildDetailSection(
                    title: langManager.getText('معلومات المسار', 'Route Information'),
                    children: [
                      _buildDetailItem(
                        label: langManager.getText('من', 'From'),
                        value: order['from'],
                        icon: Bootstrap.geo_alt,
                      ),
                      _buildDetailItem(
                        label: langManager.getText('إلى', 'To'),
                        value: order['to'],
                        icon: Bootstrap.pin_map,
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // معلومات السائق
                  _buildDetailSection(
                    title: langManager.getText('معلومات السائق', 'Driver Information'),
                    children: [
                      _buildDetailItem(
                        label: langManager.getText('السائق', 'Driver'),
                        value: order['driver'] ?? langManager.getText('لا يوجد سائق', 'No driver assigned'),
                        icon: order['driver'] != null ? Bootstrap.person_check : Bootstrap.person_x,
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // معلومات التوقيت أو الموقع المباشر
                  if (order['status'] == 'قيد التنفيذ' && order['driver'] != null) ...[
                    // للطلبات قيد التنفيذ - عرض الموقع المباشر ووقت الوصول
                    _buildDetailSection(
                      title: langManager.getText('الموقع المباشر', 'Live Location'),
                      children: [
                        _buildRealLocationItem(
                          label: langManager.getText('موقع السائق الحقيقي', 'Real Driver Location'),
                          value: langManager.getText('موقع مباشر من GPS', 'Live GPS Location'),
                          icon: Bootstrap.geo_alt_fill,
                          driverId: order['driverId'] ?? 'driver_${order['id']}',
                          driverName: order['driver'] ?? langManager.getText('السائق', 'Driver'),
                          langManager: langManager,
                        ),
                        _buildDetailItem(
                          label: langManager.getText('الوقت المقدر للوصول', 'Estimated Arrival Time'),
                          value: '25 دقيقة',
                          icon: Bootstrap.clock_fill,
                        ),
                        _buildDetailItem(
                          label: langManager.getText('المسافة المتبقية', 'Remaining Distance'),
                          value: '12.5 كم',
                          icon: Bootstrap.signpost,
                        ),
                      ],
                    ),
                  ] else ...[
                    // للطلبات الأخرى - عرض معلومات التوقيت العادية
                    _buildDetailSection(
                      title: langManager.getText('معلومات التوقيت', 'Timing Information'),
                      children: [
                        _buildDetailItem(
                          label: langManager.getText('التاريخ', 'Date'),
                          value: order['date'],
                          icon: Bootstrap.calendar,
                        ),
                        _buildDetailItem(
                          label: langManager.getText('الوقت', 'Time'),
                          value: order['time'],
                          icon: Bootstrap.clock,
                        ),
                      ],
                    ),
                  ],

                  const SizedBox(height: 100),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.tajawal(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildDetailItem({
    required String label,
    required String value,
    required IconData icon,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// عنصر الموقع الحقيقي للسائق
  Widget _buildRealLocationItem({
    required String label,
    required String value,
    required IconData icon,
    required String driverId,
    required String driverName,
    required LanguageManager langManager,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _openDriverRealLocation(driverId, driverName),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.success.withOpacity(0.1),
                  AppColors.primary.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.success.withOpacity(0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.success.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.success.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.success,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            label,
                            style: GoogleFonts.tajawal(
                              fontSize: 12,
                              color: AppColors.success,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: AppColors.success,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            langManager.getText('مباشر', 'LIVE'),
                            style: GoogleFonts.tajawal(
                              fontSize: 10,
                              color: AppColors.success,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        value,
                        style: GoogleFonts.tajawal(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        langManager.getText('اضغط لفتح الموقع الحقيقي', 'Tap to open real location'),
                        style: GoogleFonts.tajawal(
                          fontSize: 11,
                          color: Colors.white.withOpacity(0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Bootstrap.arrow_up_right_square,
                  color: AppColors.success,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLiveLocationItem({
    required String label,
    required String value,
    required IconData icon,
    required double latitude,
    required double longitude,
    required LanguageManager langManager,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _openLocationInMaps(latitude, longitude, langManager),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.success.withOpacity(0.1),
                  AppColors.primary.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.success.withOpacity(0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.success.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.success.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.success,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            label,
                            style: GoogleFonts.tajawal(
                              fontSize: 12,
                              color: AppColors.success,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: AppColors.success,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            langManager.getText('مباشر', 'LIVE'),
                            style: GoogleFonts.tajawal(
                              fontSize: 10,
                              color: AppColors.success,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        value,
                        style: GoogleFonts.tajawal(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        langManager.getText('اضغط لفتح الخريطة', 'Tap to open map'),
                        style: GoogleFonts.tajawal(
                          fontSize: 11,
                          color: Colors.white.withOpacity(0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Bootstrap.arrow_up_right_square,
                  color: AppColors.success,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// فتح موقع السائق الحقيقي في الخرائط الخارجية
  Future<void> _openDriverRealLocation(String driverId, String driverName) async {
    await DriverLocationService.openDriverRealLocation(
      context: context,
      driverId: driverId,
      driverName: driverName,
    );
  }

  /// فتح موقع محدد (للتوافق مع الكود القديم)
  Future<void> _openLocationInMaps(double latitude, double longitude, LanguageManager langManager) async {
    await DriverLocationService.openLocationInMaps(
      context: context,
      latitude: latitude,
      longitude: longitude,
      label: langManager.getText('موقع السائق', 'Driver Location'),
    );
  }
}

// كلاس رسم الخلفية المتحركة
class OrderManagementBackgroundPainter extends CustomPainter {
  final double animationValue;

  OrderManagementBackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.03)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // رسم شبكة متحركة
    final gridSize = 60.0;
    final offset = animationValue * gridSize;

    // خطوط عمودية
    for (double x = -gridSize + (offset % gridSize); x < size.width + gridSize; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // خطوط أفقية
    for (double y = -gridSize + (offset % gridSize); y < size.height + gridSize; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // رسم أشكال متحركة
    final shapePaint = Paint()
      ..color = Colors.white.withOpacity(0.02)
      ..style = PaintingStyle.fill;

    // مربعات متحركة
    for (int i = 0; i < 4; i++) {
      final angle = (animationValue * 2 * math.pi) + (i * math.pi / 2);
      final radius = 100.0 + (i * 40);
      final centerX = size.width / 2 + math.cos(angle) * radius;
      final centerY = size.height / 2 + math.sin(angle) * radius;

      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromCenter(
            center: Offset(centerX, centerY),
            width: 30.0 + (i * 10),
            height: 30.0 + (i * 10),
          ),
          const Radius.circular(8),
        ),
        shapePaint,
      );
    }
  }

  @override
  bool shouldRepaint(OrderManagementBackgroundPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}