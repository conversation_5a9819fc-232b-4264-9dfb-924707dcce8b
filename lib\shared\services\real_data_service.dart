import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:uuid/uuid.dart';
import '../models/index.dart';
import 'supabase_service.dart';

/// خدمة البيانات الحقيقية باستخدام Supabase
class RealDataService {
  static final RealDataService _instance = RealDataService._internal();
  factory RealDataService() => _instance;
  RealDataService._internal();

  final SupabaseService _supabaseService = SupabaseService();
  
  // Controllers للتحديثات المباشرة
  final StreamController<List<Driver>> _driversController = StreamController<List<Driver>>.broadcast();
  final StreamController<List<Order>> _ordersController = StreamController<List<Order>>.broadcast();
  final StreamController<User?> _userController = StreamController<User?>.broadcast();

  // Streams للاستماع للتحديثات
  Stream<List<Driver>> get driversStream => _driversController.stream;
  Stream<List<Order>> get ordersStream => _ordersController.stream;
  Stream<User?> get userStream => _userController.stream;

  // المستخدم الحالي
  User? _currentUser;
  User? get currentUser => _currentUser;

  // ═══════════════════════════════════════════════════════════════
  // خدمات المصادقة
  // ═══════════════════════════════════════════════════════════════

  /// تسجيل الدخول
  Future<User?> login(String email, String password, UserType userType) async {
    try {
      print('🔐 محاولة تسجيل الدخول: $email');

      // البحث عن المستخدم في جدول users باستخدام رقم الهاتف أو الإيميل
      final userData = await _supabaseService.getUserByPhoneOrEmail(email);

      if (userData == null) {
        print('❌ لم يتم العثور على المستخدم: $email');
        throw Exception('رقم الهاتف أو كلمة المرور غير صحيحة');
      }

      print('✅ تم العثور على المستخدم: ${userData['name']}');

      // التحقق من نوع المستخدم
      if (userData['user_type'] != userType.name) {
        print('❌ نوع المستخدم غير متطابق: ${userData['user_type']} != ${userType.name}');
        throw Exception('نوع المستخدم غير صحيح');
      }

      // التحقق من كلمة المرور (مؤقت - نقبل أي كلمة مرور للاختبار)
      if (password.length < 6) {
        print('❌ كلمة المرور قصيرة جداً');
        throw Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      }

      print('✅ تم التحقق من كلمة المرور');

      // إنشاء الكائن المناسب حسب نوع المستخدم
      if (userData['user_type'] == 'driver') {
        // الحصول على بيانات السائق الإضافية
        final driverData = await _supabaseService.getDriverByUserId(userData['id']);
        if (driverData != null) {
          print('✅ تم العثور على بيانات السائق');
          // دمج بيانات المستخدم مع بيانات السائق
          final combinedData = Map<String, dynamic>.from(userData);
          combinedData.addAll(driverData);
          _currentUser = Driver.fromJson(combinedData);
        } else {
          print('❌ لم يتم العثور على بيانات السائق');
          throw Exception('بيانات السائق غير مكتملة');
        }
      } else if (userData['user_type'] == 'company') {
        // الحصول على بيانات الشركة الإضافية
        final companyData = await _supabaseService.getCompanyByUserId(userData['id']);
        if (companyData != null) {
          print('✅ تم العثور على بيانات الشركة');
          // دمج بيانات المستخدم مع بيانات الشركة
          final combinedData = Map<String, dynamic>.from(userData);
          combinedData.addAll(companyData);
          _currentUser = Company.fromJson(combinedData);
        } else {
          print('❌ لم يتم العثور على بيانات الشركة');
          throw Exception('بيانات الشركة غير مكتملة');
        }
      } else {
        print('❌ نوع مستخدم غير معروف: ${userData['user_type']}');
        throw Exception('نوع مستخدم غير صحيح');
      }

      _currentUser = _currentUser;

      // تعيين معرف المستخدم في خدمة Supabase لرفع الصور
      _supabaseService.setCurrentUserId(userData['id']);

      _userController.add(_currentUser);
      print('🎉 تم تسجيل الدخول بنجاح: ${_currentUser?.name}');
      return _currentUser;
    } catch (e) {
      throw Exception('خطأ في تسجيل الدخول: $e');
    }
  }

  /// تسجيل حساب سائق جديد (بدون Supabase Auth)
  Future<Driver?> registerDriver({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String phone,
    required String vehicleType,
    required String plateNumber,
    required String vehicleModel,
    required int manufacturingYear,
    required String drivingLicenseNumber,
    required DateTime licenseExpiryDate,
  }) async {
    try {
      print('🚀 بدء تسجيل السائق: $phone');

      // إنشاء UUID فريد للمستخدم
      final uuid = Uuid();
      final userId = uuid.v4();
      print('📝 إنشاء معرف فريد: $userId');

      // إنشاء سجل في جدول المستخدمين مباشرة
      final userRecord = {
        'id': userId,
        'email': email,
        'name': '$firstName $lastName',
        'phone': phone,
        'user_type': 'driver',
        'status': 'active',
        'verification_status': 'pending',
      };

      print('📝 إنشاء سجل في جدول المستخدمين...');
      await _supabaseService.createUserDirect(userRecord);
      print('✅ تم إنشاء سجل المستخدم بنجاح');

      // إنشاء سجل في جدول السائقين
      final driverRecord = {
        'user_id': userId,
        'vehicle_type': vehicleType,
        'plate_number': plateNumber,
        'vehicle_model': vehicleModel,
        'manufacturing_year': manufacturingYear,
        'driving_license_number': drivingLicenseNumber,
        'license_expiry_date': licenseExpiryDate.toIso8601String(),
        'rating': 0.0,
        'total_trips': 0,
        'total_earnings': 0.0,
        'driver_status': 'offline',
        'service_areas': ['بغداد', 'البصرة', 'أربيل'],
      };

      print('📝 إنشاء سجل في جدول السائقين...');
      final driverId = await _supabaseService.createDriver(driverRecord);
      print('✅ تم إنشاء سجل السائق بنجاح: $driverId');

      // إنشاء كائن Driver
      final driver = Driver(
        id: driverId,
        name: '$firstName $lastName',
        email: email,
        phone: phone,
        createdAt: DateTime.now(),
        vehicleType: vehicleType,
        plateNumber: plateNumber,
        vehicleModel: vehicleModel,
        manufacturingYear: manufacturingYear,
        drivingLicenseNumber: drivingLicenseNumber,
        licenseExpiryDate: licenseExpiryDate,
        verificationStatus: VerificationStatus.pending,
        serviceAreas: ['بغداد', 'البصرة', 'أربيل'],
      );

      _currentUser = driver;

      // تعيين معرف المستخدم في خدمة Supabase لرفع الصور
      _supabaseService.setCurrentUserId(userId);

      _userController.add(_currentUser);
      print('🎉 تم تسجيل السائق بنجاح!');
      return driver;
    } catch (e) {
      print('❌ خطأ في تسجيل السائق: $e');
      throw Exception('خطأ في تسجيل السائق: $e');
    }
  }

  /// تسجيل حساب شركة جديدة (بدون Supabase Auth)
  Future<Company?> registerCompany({
    required String email,
    required String password,
    required String companyName,
    required String phone,
    required String businessLicense,
    required String address,
    String? website,
    String? description,
  }) async {
    try {
      print('🏢 بدء تسجيل الشركة: $phone');

      // إنشاء UUID فريد للمستخدم
      final uuid = Uuid();
      final userId = uuid.v4();
      print('📝 إنشاء معرف فريد: $userId');

      // إنشاء سجل في جدول المستخدمين مباشرة
      final userRecord = {
        'id': userId,
        'email': email,
        'name': companyName,
        'phone': phone,
        'user_type': 'company',
        'status': 'active',
        'verification_status': 'pending',
      };

      print('📝 إنشاء سجل في جدول المستخدمين...');
      await _supabaseService.createUserDirect(userRecord);
      print('✅ تم إنشاء سجل المستخدم بنجاح');

      // إنشاء سجل في جدول الشركات
      final companyRecord = {
        'user_id': userId,
        'business_license': businessLicense,
        'address': address,
        'website': website,
        'description': description,
        'service_areas': ['بغداد', 'البصرة', 'أربيل', 'النجف'],
        'vehicle_types': [
          'أتيكو 6 متر',
          'أتيكو 10 متر',
          'شاحنة جادر متحرك 13.60م',
        ],
        'rating': 0.0,
        'total_orders': 0,
        'active_orders': 0,
        'total_payments': 0.0,
        'subscription': 'basic',
        'subscription_expiry': DateTime.now().add(const Duration(days: 30)).toIso8601String(),
      };

      print('📝 إنشاء سجل في جدول الشركات...');
      final companyId = await _supabaseService.createCompany(companyRecord);
      print('✅ تم إنشاء سجل الشركة بنجاح: $companyId');

      // إنشاء كائن Company
      final company = Company(
        id: companyId,
        name: companyName,
        email: email,
        phone: phone,
        createdAt: DateTime.now(),
        businessLicense: businessLicense,
        address: address,
        website: website,
        description: description,
        verificationStatus: VerificationStatus.pending,
        subscription: CompanySubscription.basic,
        subscriptionExpiry: DateTime.now().add(const Duration(days: 30)),
        serviceAreas: ['بغداد', 'البصرة', 'أربيل', 'النجف'],
        vehicleTypes: [
          'أتيكو 6 متر',
          'أتيكو 10 متر',
          'شاحنة جادر متحرك 13.60م',
        ],
      );

      _currentUser = company;

      // تعيين معرف المستخدم في خدمة Supabase لرفع الصور
      _supabaseService.setCurrentUserId(userId);

      _userController.add(_currentUser);
      print('🎉 تم تسجيل الشركة بنجاح!');
      return company;
    } catch (e) {
      print('❌ خطأ في تسجيل الشركة: $e');
      throw Exception('خطأ في تسجيل الشركة: $e');
    }
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    try {
      print('🚪 تسجيل الخروج...');
      _currentUser = null;

      // إزالة معرف المستخدم من خدمة Supabase
      _supabaseService.setCurrentUserId(null);

      _userController.add(null);
      print('✅ تم تسجيل الخروج بنجاح');
    } catch (e) {
      print('❌ خطأ في تسجيل الخروج: $e');
      throw Exception('خطأ في تسجيل الخروج: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // خدمات السائقين
  // ═══════════════════════════════════════════════════════════════

  /// الحصول على السائقين المتوفرين
  Future<List<Driver>> getAvailableDrivers({
    Position? nearLocation,
    double maxDistance = 50.0,
    String? vehicleType,
  }) async {
    try {
      final driversData = await _supabaseService.getAvailableDrivers(
        nearLocation: nearLocation,
        maxDistance: maxDistance,
        vehicleType: vehicleType,
      );

      final drivers = driversData.map((data) {
        // دمج بيانات المستخدم مع بيانات السائق
        final userData = data['users'] as Map<String, dynamic>;
        final driverData = Map<String, dynamic>.from(data);
        driverData.addAll(userData);
        
        return Driver.fromJson(driverData);
      }).toList();

      _driversController.add(drivers);
      return drivers;
    } catch (e) {
      throw Exception('خطأ في جلب السائقين المتوفرين: $e');
    }
  }

  /// تحديث موقع السائق
  Future<void> updateDriverLocation(String driverId, Position position) async {
    try {
      await _supabaseService.updateDriverLocation(driverId, position);
    } catch (e) {
      throw Exception('خطأ في تحديث موقع السائق: $e');
    }
  }

  /// تحديث حالة السائق
  Future<void> updateDriverStatus(String driverId, DriverStatus status) async {
    try {
      await _supabaseService.updateDriverStatus(driverId, status.name);
    } catch (e) {
      throw Exception('خطأ في تحديث حالة السائق: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // خدمات الطلبات
  // ═══════════════════════════════════════════════════════════════

  /// إنشاء طلب جديد
  Future<String> createOrder(Order order) async {
    try {
      final orderData = order.toJson();
      // تحويل البيانات لتناسب قاعدة البيانات
      orderData['pickup_location'] = {
        'latitude': order.pickupLocation.latitude,
        'longitude': order.pickupLocation.longitude,
        'address': order.pickupLocation.address,
        'contact_name': order.pickupLocation.contactName,
        'contact_phone': order.pickupLocation.contactPhone,
        'instructions': order.pickupLocation.instructions,
      };
      orderData['delivery_location'] = {
        'latitude': order.deliveryLocation.latitude,
        'longitude': order.deliveryLocation.longitude,
        'address': order.deliveryLocation.address,
        'contact_name': order.deliveryLocation.contactName,
        'contact_phone': order.deliveryLocation.contactPhone,
        'instructions': order.deliveryLocation.instructions,
      };

      final orderId = await _supabaseService.createOrder(orderData);
      return orderId;
    } catch (e) {
      throw Exception('خطأ في إنشاء الطلب: $e');
    }
  }

  /// الحصول على طلبات الشركة
  Future<List<Order>> getOrdersForCompany(String companyId) async {
    try {
      final ordersData = await _supabaseService.getCompanyOrders(companyId);
      final orders = ordersData.map((data) => _mapToOrder(data)).toList();
      _ordersController.add(orders);
      return orders;
    } catch (e) {
      throw Exception('خطأ في جلب طلبات الشركة: $e');
    }
  }

  /// الحصول على طلبات السائق
  Future<List<Order>> getOrdersForDriver(String driverId) async {
    try {
      final ordersData = await _supabaseService.getDriverOrders(driverId);
      final orders = ordersData.map((data) => _mapToOrder(data)).toList();
      _ordersController.add(orders);
      return orders;
    } catch (e) {
      throw Exception('خطأ في جلب طلبات السائق: $e');
    }
  }

  /// الحصول على الطلبات المتاحة للسائق
  Future<List<Order>> getAvailableOrdersForDriver(String driverId) async {
    try {
      final ordersData = await _supabaseService.getAvailableOrdersForDriver(driverId);
      final orders = ordersData.map((data) => _mapToOrder(data)).toList();
      return orders;
    } catch (e) {
      throw Exception('خطأ في جلب الطلبات المتاحة: $e');
    }
  }

  /// تحديث حالة الطلب
  Future<void> updateOrderStatus(String orderId, OrderStatus status, {String? note}) async {
    try {
      await _supabaseService.updateOrderStatus(orderId, status.name, note: note);
    } catch (e) {
      throw Exception('خطأ في تحديث حالة الطلب: $e');
    }
  }

  /// تعيين سائق للطلب
  Future<void> assignDriverToOrder(String orderId, String driverId) async {
    try {
      await _supabaseService.assignDriverToOrder(orderId, driverId);
    } catch (e) {
      throw Exception('خطأ في تعيين السائق للطلب: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال مساعدة
  // ═══════════════════════════════════════════════════════════════

  /// تحويل بيانات قاعدة البيانات إلى كائن Order
  Order _mapToOrder(Map<String, dynamic> data) {
    final pickupLocationData = data['pickup_location'] as Map<String, dynamic>;
    final deliveryLocationData = data['delivery_location'] as Map<String, dynamic>;

    return Order(
      id: data['id'],
      companyId: data['company_id'],
      driverId: data['driver_id'],
      cargoType: data['cargo_type'],
      weight: (data['weight'] as num).toDouble(),
      vehicleType: data['vehicle_type'],
      status: OrderStatus.values.firstWhere((e) => e.name == data['status']),
      priority: OrderPriority.values.firstWhere((e) => e.name == data['priority']),
      paymentMethod: PaymentMethod.values.firstWhere((e) => e.name == data['payment_method']),
      price: (data['price'] as num).toDouble(),
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: data['updated_at'] != null ? DateTime.parse(data['updated_at']) : null,
      scheduledPickupTime: data['scheduled_pickup_time'] != null 
          ? DateTime.parse(data['scheduled_pickup_time']) : null,
      scheduledDeliveryTime: data['scheduled_delivery_time'] != null 
          ? DateTime.parse(data['scheduled_delivery_time']) : null,
      actualPickupTime: data['actual_pickup_time'] != null 
          ? DateTime.parse(data['actual_pickup_time']) : null,
      actualDeliveryTime: data['actual_delivery_time'] != null 
          ? DateTime.parse(data['actual_delivery_time']) : null,
      pickupLocation: OrderLocation(
        latitude: (pickupLocationData['latitude'] as num).toDouble(),
        longitude: (pickupLocationData['longitude'] as num).toDouble(),
        address: pickupLocationData['address'],
        contactName: pickupLocationData['contact_name'],
        contactPhone: pickupLocationData['contact_phone'],
        instructions: pickupLocationData['instructions'],
      ),
      deliveryLocation: OrderLocation(
        latitude: (deliveryLocationData['latitude'] as num).toDouble(),
        longitude: (deliveryLocationData['longitude'] as num).toDouble(),
        address: deliveryLocationData['address'],
        contactName: deliveryLocationData['contact_name'],
        contactPhone: deliveryLocationData['contact_phone'],
        instructions: deliveryLocationData['instructions'],
      ),
      description: data['description'],
      specialInstructions: data['special_instructions'],
      images: data['images'] != null 
          ? List<String>.from(data['images']) : null,
      metadata: data['metadata'],
    );
  }

  /// تنظيف الموارد
  void dispose() {
    _driversController.close();
    _ordersController.close();
    _userController.close();
  }
}
