import 'package:supabase_flutter/supabase_flutter.dart';

/// خدمة معالجة أخطاء Storage
class StorageErrorHandler {
  StorageErrorHandler._();

  /// معالجة أخطاء Storage وإرجاع رسائل مفهومة
  static String handleStorageError(dynamic error) {
    if (error is StorageException) {
      return _handleStorageException(error);
    } else if (error is Exception) {
      return _handleGenericException(error);
    } else {
      return 'حدث خطأ غير متوقع: $error';
    }
  }

  /// معالجة أخطاء StorageException المحددة
  static String _handleStorageException(StorageException error) {
    switch (error.statusCode) {
      case '400':
        return 'طلب غير صحيح. تحقق من البيانات المرسلة';
      case '401':
        return 'غير مصرح لك بالوصول. يرجى تسجيل الدخول مرة أخرى';
      case '403':
        return 'ليس لديك صلاحية للوصول لهذا الملف أو المجلد';
      case '404':
        return 'الملف المطلوب غير موجود';
      case '409':
        return 'الملف موجود مسبقاً. يرجى اختيار اسم آخر';
      case '413':
        return 'حجم الملف كبير جداً. الحد الأقصى 5MB';
      case '415':
        return 'نوع الملف غير مدعوم. يرجى استخدام JPG, PNG, أو WEBP';
      case '422':
        return 'بيانات الملف غير صحيحة';
      case '429':
        return 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً';
      case '500':
        return 'خطأ في الخادم. يرجى المحاولة لاحقاً';
      case '503':
        return 'الخدمة غير متوفرة حالياً. يرجى المحاولة لاحقاً';
      default:
        return 'خطأ في التخزين: ${error.message ?? 'خطأ غير معروف'}';
    }
  }

  /// معالجة الأخطاء العامة
  static String _handleGenericException(Exception error) {
    final message = error.toString();
    
    if (message.contains('network')) {
      return 'مشكلة في الاتصال بالإنترنت. تحقق من اتصالك';
    } else if (message.contains('timeout')) {
      return 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى';
    } else if (message.contains('permission')) {
      return 'ليس لديك صلاحية لتنفيذ هذا الإجراء';
    } else if (message.contains('file not found')) {
      return 'الملف غير موجود';
    } else if (message.contains('invalid file')) {
      return 'الملف غير صحيح أو تالف';
    } else {
      return 'حدث خطأ: ${error.toString()}';
    }
  }

  /// التحقق من صحة نوع الملف
  static bool isValidFileType(String fileName) {
    final validExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
    final extension = fileName.toLowerCase().split('.').last;
    return validExtensions.contains('.$extension');
  }

  /// التحقق من صحة حجم الملف (5MB كحد أقصى)
  static bool isValidFileSize(int fileSizeInBytes) {
    const maxSizeInBytes = 5 * 1024 * 1024; // 5MB
    return fileSizeInBytes <= maxSizeInBytes;
  }

  /// إنشاء مسار آمن للملف بناءً على UID المستخدم
  static String createUserFilePath(String userId, String fileName) {
    // التأكد من أن اسم الملف آمن
    final safeName = _sanitizeFileName(fileName);
    return '$userId/$safeName';
  }

  /// إنشاء مسار آمن لصور الطلبات
  static String createOrderFilePath(String orderId, String fileName) {
    final safeName = _sanitizeFileName(fileName);
    return '$orderId/$safeName';
  }

  /// تنظيف اسم الملف من الأحرف الخطيرة
  static String _sanitizeFileName(String fileName) {
    // إزالة الأحرف الخطيرة والمسافات
    return fileName
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')
        .replaceAll(' ', '_')
        .toLowerCase();
  }

  /// رسائل الخطأ المخصصة لكل نوع bucket
  static String getPermissionErrorMessage(String bucketName) {
    switch (bucketName) {
      case 'profile-images':
        return 'ليس لديك صلاحية لرفع الصور الشخصية في هذا المجلد';
      case 'vehicle-images':
        return 'ليس لديك صلاحية لرفع صور المركبات في هذا المجلد';
      case 'order-images':
        return 'ليس لديك صلاحية للوصول لصور هذا الطلب';
      default:
        return 'ليس لديك صلاحية للوصول لهذا المجلد';
    }
  }

  /// رسائل النجاح المخصصة
  static String getSuccessMessage(String operation, String bucketName) {
    final bucketDisplayName = _getBucketDisplayName(bucketName);
    
    switch (operation) {
      case 'upload':
        return 'تم رفع $bucketDisplayName بنجاح';
      case 'delete':
        return 'تم حذف $bucketDisplayName بنجاح';
      case 'update':
        return 'تم تحديث $bucketDisplayName بنجاح';
      default:
        return 'تمت العملية بنجاح';
    }
  }

  /// الحصول على اسم عرض للـ bucket
  static String _getBucketDisplayName(String bucketName) {
    switch (bucketName) {
      case 'profile-images':
        return 'الصورة الشخصية';
      case 'vehicle-images':
        return 'صورة المركبة';
      case 'order-images':
        return 'صورة الطلب';
      default:
        return 'الصورة';
    }
  }

  /// التحقق من صحة مسار الملف
  static bool isValidFilePath(String filePath, String userId) {
    // التأكد من أن المسار يبدأ بـ UID المستخدم
    return filePath.startsWith('$userId/');
  }

  /// التحقق من صحة مسار صور الطلبات
  static bool isValidOrderFilePath(String filePath, String orderId) {
    // التأكد من أن المسار يبدأ بـ order ID
    return filePath.startsWith('$orderId/');
  }
}
