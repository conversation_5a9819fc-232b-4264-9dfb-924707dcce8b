import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:icons_plus/icons_plus.dart';
import 'dart:math' as math;

import '../../core/constants/app_colors.dart';
import '../../core/utils/language_manager.dart';
import '../../shared/providers/auth_provider.dart';
import '../../shared/models/user.dart';
import 'company_register_screen.dart';

class CompanyLoginScreen extends StatefulWidget {
  const CompanyLoginScreen({super.key});

  @override
  State<CompanyLoginScreen> createState() => _CompanyLoginScreenState();
}

class _CompanyLoginScreenState extends State<CompanyLoginScreen>
    with TickerProviderStateMixin {

  // ═══════════════════════════════════════════════════════════════
  // متحكمات النصوص والنماذج (Text Controllers & Form)
  // ═══════════════════════════════════════════════════════════════

  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  // ═══════════════════════════════════════════════════════════════
  // متغيرات الحالة (State Variables)
  // ═══════════════════════════════════════════════════════════════

  bool _isPasswordVisible = false;
  bool _rememberMe = false;
  bool _isLoading = false;

  // ═══════════════════════════════════════════════════════════════
  // متحكمات الأنيميشن (Animation Controllers)
  // ═══════════════════════════════════════════════════════════════

  late AnimationController _fadeController;
  late AnimationController _backgroundController;
  late AnimationController _slideController;

  late Animation<double> _fadeAnimation;
  late Animation<double> _backgroundAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    // أنيميشن الظهور التدريجي
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // أنيميشن الخلفية المتحركة
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 12),
      vsync: this,
    );

    // أنيميشن الانزلاق
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.linear,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() {
    _fadeController.forward();
    // إيقاف أنيميشن الخلفية المتكرر
    // _backgroundController.repeat();

    // عرض العناصر مباشرة
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _backgroundController.dispose();
    _slideController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // الخلفية الثابتة
          _buildStaticBackground(),

              // المحتوى الرئيسي
              SafeArea(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    children: [
                      // زر العودة
                      _buildBackButton(),

                      const SizedBox(height: 20),

                      // الهيدر مع الشعار
                      _buildHeader(),

                      const SizedBox(height: 60),

                      // نموذج تسجيل الدخول
                      _buildLoginForm(),

                      const SizedBox(height: 30),

                      // خيارات إضافية
                      _buildAdditionalOptions(),

                      const SizedBox(height: 40),
                    ],
                  ),
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildStaticBackground() {
    return Positioned.fill(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF1E3A8A), // أزرق داكن
              Color(0xFF3B82F6), // أزرق متوسط
              Color(0xFF1E40AF), // أزرق عميق
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: GestureDetector(
        onTap: () => Navigator.pop(context),
        child: Container(
          width: 44,
          height: 44,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: const Icon(
            Bootstrap.arrow_left,
            color: Colors.white,
            size: 20,
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Column(
            children: [
              // أيقونة المبنى الكبيرة
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: const Icon(
                  Bootstrap.building,
                  color: Colors.white,
                  size: 60,
                ),
              ),

              const SizedBox(height: 24),

              // العنوان الرئيسي
              Text(
                langManager.getText('تسجيل دخول الشركة', 'Company Login'),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: Colors.black26,
                      offset: Offset(0, 2),
                      blurRadius: 4,
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 8),

              // العنوان الفرعي
              Text(
                langManager.getText(
                  'إدارة عمليات النقل بذكاء واحترافية',
                  'Manage transport operations smartly and professionally',
                ),
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLoginForm() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: const Color(0xFF1A1A1A).withOpacity(0.95),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: Colors.white.withOpacity(0.1),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 30,
                    offset: const Offset(0, 15),
                  ),
                ],
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    // حقل البريد الإلكتروني
                    _buildEmailField(langManager),

                    const SizedBox(height: 20),

                    // حقل كلمة المرور
                    _buildPasswordField(langManager),

                    const SizedBox(height: 20),

                    // خيار تذكر البيانات
                    _buildRememberMeOption(langManager),

                    const SizedBox(height: 30),

                    // زر تسجيل الدخول
                    _buildLoginButton(langManager),

                    const SizedBox(height: 20),

                    // روابط نسيت كلمة المرور وإنشاء حساب
                    _buildBottomLinks(langManager),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmailField(LanguageManager langManager) {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        labelText: langManager.getText('البريد الإلكتروني', 'Email Address'),
        labelStyle: const TextStyle(color: Colors.white70),
        hintText: langManager.getText('أدخل البريد الإلكتروني للشركة', 'Enter company email'),
        hintStyle: const TextStyle(color: Colors.white54),
        prefixIcon: Container(
          margin: const EdgeInsets.all(12),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Bootstrap.envelope,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        filled: true,
        fillColor: Colors.white.withOpacity(0.1),
      ),
      style: const TextStyle(color: Colors.white),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return langManager.getText('يرجى إدخال البريد الإلكتروني', 'Please enter email address');
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return langManager.getText('البريد الإلكتروني غير صحيح', 'Invalid email address');
        }
        return null;
      },
    );
  }

  Widget _buildPasswordField(LanguageManager langManager) {
    return TextFormField(
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      textInputAction: TextInputAction.done,
      decoration: InputDecoration(
        labelText: langManager.getText('كلمة المرور', 'Password'),
        labelStyle: const TextStyle(color: Colors.white70),
        hintText: langManager.getText('أدخل كلمة المرور', 'Enter password'),
        hintStyle: const TextStyle(color: Colors.white54),
        prefixIcon: Container(
          margin: const EdgeInsets.all(12),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Bootstrap.lock,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        suffixIcon: IconButton(
          onPressed: () {
            setState(() {
              _isPasswordVisible = !_isPasswordVisible;
            });
          },
          icon: Icon(
            _isPasswordVisible ? Bootstrap.eye_slash : Bootstrap.eye,
            color: Colors.white70,
          ),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        filled: true,
        fillColor: Colors.white.withOpacity(0.1),
      ),
      style: const TextStyle(color: Colors.white),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return langManager.getText('يرجى إدخال كلمة المرور', 'Please enter password');
        }
        if (value.length < 6) {
          return langManager.getText('كلمة المرور قصيرة جداً', 'Password is too short');
        }
        return null;
      },
    );
  }

  Widget _buildRememberMeOption(LanguageManager langManager) {
    return Row(
      children: [
        Checkbox(
          value: _rememberMe,
          onChanged: (value) {
            setState(() {
              _rememberMe = value ?? false;
            });
          },
          activeColor: AppColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        Text(
          langManager.getText('تذكر بياناتي', 'Remember me'),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildLoginButton(LanguageManager langManager) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleLogin,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 8,
          shadowColor: AppColors.primary.withOpacity(0.4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                langManager.getText('تسجيل الدخول', 'Login'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Widget _buildBottomLinks(LanguageManager langManager) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // رابط نسيت كلمة المرور
        TextButton(
          onPressed: _handleForgotPassword,
          child: Text(
            langManager.getText('نسيت كلمة المرور؟', 'Forgot Password?'),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
              decoration: TextDecoration.underline,
            ),
          ),
        ),

        // رابط إنشاء حساب جديد
        TextButton(
          onPressed: _navigateToSignUp,
          child: Text(
            langManager.getText('إنشاء حساب', 'Sign Up'),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAdditionalOptions() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Text(
                  langManager.getText('خيارات تسجيل الدخول الإضافية', 'Additional Login Options'),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),

                const SizedBox(height: 20),

                Row(
                  children: [
                    // تسجيل دخول بالبصمة
                    Expanded(
                      child: _buildBiometricLoginButton(langManager),
                    ),

                    const SizedBox(width: 16),

                    // تسجيل دخول برمز QR
                    Expanded(
                      child: _buildQRLoginButton(langManager),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBiometricLoginButton(LanguageManager langManager) {
    return GestureDetector(
      onTap: _handleBiometricLogin,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            const Icon(
              Bootstrap.fingerprint,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              langManager.getText('البصمة', 'Biometric'),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQRLoginButton(LanguageManager langManager) {
    return GestureDetector(
      onTap: _handleQRLogin,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            const Icon(
              Bootstrap.qr_code,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              langManager.getText('رمز QR', 'QR Code'),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال التعامل مع الأحداث (Event Handlers)
  // ═══════════════════════════════════════════════════════════════

  void _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final langManager = Provider.of<LanguageManager>(context, listen: false);

      // محاولة تسجيل الدخول الحقيقي
      final success = await authProvider.login(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        userType: UserType.company,
        rememberMe: _rememberMe,
      );

      if (success && mounted) {
        // نجح تسجيل الدخول - الانتقال لشاشة لوحة التحكم
        Navigator.pushReplacementNamed(context, '/company-dashboard');
      } else if (mounted) {
        // فشل تسجيل الدخول - عرض رسالة الخطأ
        final errorMessage = authProvider.errorMessage ??
            langManager.getText('بيانات تسجيل الدخول غير صحيحة', 'Invalid login credentials');
        _showErrorMessage(errorMessage, errorMessage);
      }
    } catch (e) {
      if (mounted) {
        final langManager = Provider.of<LanguageManager>(context, listen: false);
        _showErrorMessage(
          langManager.getText('حدث خطأ أثناء تسجيل الدخول', 'Login error occurred'),
          'Login error occurred: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleForgotPassword() {
    final langManager = Provider.of<LanguageManager>(context, listen: false);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            langManager.getText('استعادة كلمة المرور', 'Password Recovery'),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(
            langManager.getText(
              'سيتم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني',
              'A password recovery link will be sent to your email',
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(langManager.getText('إلغاء', 'Cancel')),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _showSuccessMessage(
                  'تم إرسال رابط الاستعادة',
                  'Recovery link sent',
                );
              },
              child: Text(langManager.getText('إرسال', 'Send')),
            ),
          ],
        );
      },
    );
  }

  void _handleBiometricLogin() {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    _showInfoMessage(
      'تسجيل الدخول بالبصمة قريباً',
      'Biometric login coming soon',
    );
  }

  void _handleQRLogin() {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    _showInfoMessage(
      'تسجيل الدخول برمز QR قريباً',
      'QR code login coming soon',
    );
  }

  void _navigateToSignUp() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CompanyRegisterScreen()),
    );
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال عرض الرسائل (Message Functions)
  // ═══════════════════════════════════════════════════════════════

  void _showSuccessMessage(String messageAr, String messageEn) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          langManager.getText(messageAr, messageEn),
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showErrorMessage(String messageAr, String messageEn) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          langManager.getText(messageAr, messageEn),
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showInfoMessage(String messageAr, String messageEn) {
    final langManager = Provider.of<LanguageManager>(context, listen: false);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          langManager.getText(messageAr, messageEn),
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.info,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}

// ═══════════════════════════════════════════════════════════════
// فئة الرسام المخصص للخلفية المتحركة
// ═══════════════════════════════════════════════════════════════

class CompanyBackgroundPainter extends CustomPainter {
  final double animationValue;

  CompanyBackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.03)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    // رسم شبكة متحركة
    final gridSize = 40.0;
    final offsetX = (animationValue * gridSize) % gridSize;
    final offsetY = (animationValue * gridSize * 0.7) % gridSize;

    // خطوط عمودية
    for (double x = -gridSize + offsetX; x < size.width + gridSize; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // خطوط أفقية
    for (double y = -gridSize + offsetY; y < size.height + gridSize; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // رسم دوائر متحركة
    final circlePaint = Paint()
      ..color = Colors.white.withOpacity(0.05)
      ..style = PaintingStyle.fill;

    final circleCount = 8;
    for (int i = 0; i < circleCount; i++) {
      final angle = (animationValue * 2 * math.pi) + (i * 2 * math.pi / circleCount);
      final radius = 60.0 + (math.sin(animationValue * 2 * math.pi + i) * 20);
      final centerX = size.width * 0.5 + math.cos(angle) * size.width * 0.3;
      final centerY = size.height * 0.5 + math.sin(angle) * size.height * 0.3;

      canvas.drawCircle(
        Offset(centerX, centerY),
        radius,
        circlePaint,
      );
    }
  }

  @override
  bool shouldRepaint(CompanyBackgroundPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}
