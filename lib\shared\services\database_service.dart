import 'dart:async';
import 'package:geolocator/geolocator.dart';
import '../models/index.dart';
import 'data_service.dart';
import 'real_data_service.dart';

/// خدمة قاعدة البيانات الموحدة
/// تتحول تلقائياً بين البيانات المحاكية والحقيقية
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  // الخدمات
  final DataService _mockDataService = DataService();
  final RealDataService _realDataService = RealDataService();
  
  // تحديد نوع الخدمة المستخدمة
  bool _useRealDatabase = true; // تغيير إلى false لاستخدام البيانات المحاكية

  // Controllers للتحديثات المباشرة
  final StreamController<List<Driver>> _driversController = StreamController<List<Driver>>.broadcast();
  final StreamController<List<Order>> _ordersController = StreamController<List<Order>>.broadcast();
  final StreamController<User?> _userController = StreamController<User?>.broadcast();

  // Streams للاستماع للتحديثات
  Stream<List<Driver>> get driversStream => _driversController.stream;
  Stream<List<Order>> get ordersStream => _ordersController.stream;
  Stream<User?> get userStream => _userController.stream;

  // المستخدم الحالي
  User? get currentUser => _useRealDatabase 
      ? _realDataService.currentUser 
      : _mockDataService.currentUser;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_useRealDatabase) {
      // محاولة الاتصال بـ Supabase
      try {
        // يمكن إضافة اختبار اتصال هنا
        print('🔗 استخدام قاعدة البيانات الحقيقية (Supabase)');
      } catch (e) {
        print('⚠️ فشل الاتصال بقاعدة البيانات، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        await _mockDataService.initializeMockData();
      }
    } else {
      print('🧪 استخدام البيانات المحاكية');
      await _mockDataService.initializeMockData();
    }
  }

  /// تبديل نوع قاعدة البيانات
  void toggleDatabaseType() {
    _useRealDatabase = !_useRealDatabase;
    print(_useRealDatabase 
        ? '🔗 تم التحول لقاعدة البيانات الحقيقية' 
        : '🧪 تم التحول للبيانات المحاكية');
  }

  // ═══════════════════════════════════════════════════════════════
  // خدمات المصادقة
  // ═══════════════════════════════════════════════════════════════

  Future<User?> login(String email, String password, UserType userType) async {
    try {
      final user = _useRealDatabase
          ? await _realDataService.login(email, password, userType)
          : await _mockDataService.login(email, password, userType);

      _userController.add(user);
      return user;
    } catch (e) {
      // لا نتحول للبيانات الوهمية في حالة فشل تسجيل الدخول
      // لأن هذا قد يكون بسبب بيانات خاطئة وليس خطأ في النظام
      print('❌ فشل تسجيل الدخول: $e');
      rethrow;
    }
  }

  Future<Driver?> registerDriver({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String phone,
    required String vehicleType,
    required String plateNumber,
    required String vehicleModel,
    required int manufacturingYear,
    required String drivingLicenseNumber,
    required DateTime licenseExpiryDate,
  }) async {
    try {
      if (_useRealDatabase) {
        return await _realDataService.registerDriver(
          email: email,
          password: password,
          firstName: firstName,
          lastName: lastName,
          phone: phone,
          vehicleType: vehicleType,
          plateNumber: plateNumber,
          vehicleModel: vehicleModel,
          manufacturingYear: manufacturingYear,
          drivingLicenseNumber: drivingLicenseNumber,
          licenseExpiryDate: licenseExpiryDate,
        );
      } else {
        // للبيانات المحاكية، نحاكي التسجيل
        final driver = Driver(
          id: 'driver_${DateTime.now().millisecondsSinceEpoch}',
          name: '$firstName $lastName',
          email: email,
          phone: phone,
          createdAt: DateTime.now(),
          vehicleType: vehicleType,
          plateNumber: plateNumber,
          vehicleModel: vehicleModel,
          manufacturingYear: manufacturingYear,
          drivingLicenseNumber: drivingLicenseNumber,
          licenseExpiryDate: licenseExpiryDate,
          verificationStatus: VerificationStatus.pending,
        );
        return driver;
      }
    } catch (e) {
      if (_useRealDatabase) {
        print('⚠️ خطأ في قاعدة البيانات الحقيقية، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        return await registerDriver(
          email: email,
          password: password,
          firstName: firstName,
          lastName: lastName,
          phone: phone,
          vehicleType: vehicleType,
          plateNumber: plateNumber,
          vehicleModel: vehicleModel,
          manufacturingYear: manufacturingYear,
          drivingLicenseNumber: drivingLicenseNumber,
          licenseExpiryDate: licenseExpiryDate,
        );
      }
      rethrow;
    }
  }

  Future<Company?> registerCompany({
    required String email,
    required String password,
    required String companyName,
    required String phone,
    required String businessLicense,
    required String address,
    String? website,
    String? description,
  }) async {
    try {
      if (_useRealDatabase) {
        return await _realDataService.registerCompany(
          email: email,
          password: password,
          companyName: companyName,
          phone: phone,
          businessLicense: businessLicense,
          address: address,
          website: website,
          description: description,
        );
      } else {
        // للبيانات المحاكية، نحاكي التسجيل
        final company = Company(
          id: 'company_${DateTime.now().millisecondsSinceEpoch}',
          name: companyName,
          email: email,
          phone: phone,
          createdAt: DateTime.now(),
          businessLicense: businessLicense,
          address: address,
          website: website,
          description: description,
          verificationStatus: VerificationStatus.pending,
          subscription: CompanySubscription.basic,
          subscriptionExpiry: DateTime.now().add(const Duration(days: 30)),
        );
        return company;
      }
    } catch (e) {
      if (_useRealDatabase) {
        print('⚠️ خطأ في قاعدة البيانات الحقيقية، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        return await registerCompany(
          email: email,
          password: password,
          companyName: companyName,
          phone: phone,
          businessLicense: businessLicense,
          address: address,
          website: website,
          description: description,
        );
      }
      rethrow;
    }
  }

  Future<void> logout() async {
    try {
      if (_useRealDatabase) {
        await _realDataService.logout();
      } else {
        await _mockDataService.logout();
      }
      _userController.add(null);
    } catch (e) {
      if (_useRealDatabase) {
        print('⚠️ خطأ في قاعدة البيانات الحقيقية، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        await logout();
      } else {
        rethrow;
      }
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // خدمات السائقين
  // ═══════════════════════════════════════════════════════════════

  Future<List<Driver>> getAvailableDrivers({
    Position? nearLocation,
    double maxDistance = 50.0,
    String? vehicleType,
  }) async {
    try {
      final drivers = _useRealDatabase
          ? await _realDataService.getAvailableDrivers(
              nearLocation: nearLocation,
              maxDistance: maxDistance,
              vehicleType: vehicleType,
            )
          : await _mockDataService.getAvailableDrivers(
              nearLocation: nearLocation,
              maxDistance: maxDistance,
              vehicleType: vehicleType,
            );
      
      _driversController.add(drivers);
      return drivers;
    } catch (e) {
      if (_useRealDatabase) {
        print('⚠️ خطأ في قاعدة البيانات الحقيقية، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        return await getAvailableDrivers(
          nearLocation: nearLocation,
          maxDistance: maxDistance,
          vehicleType: vehicleType,
        );
      }
      rethrow;
    }
  }

  Future<void> updateDriverLocation(String driverId, Position position) async {
    try {
      if (_useRealDatabase) {
        await _realDataService.updateDriverLocation(driverId, position);
      } else {
        await _mockDataService.updateDriverLocation(driverId, position);
      }
    } catch (e) {
      if (_useRealDatabase) {
        print('⚠️ خطأ في قاعدة البيانات الحقيقية، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        await updateDriverLocation(driverId, position);
      } else {
        rethrow;
      }
    }
  }

  Future<void> updateDriverStatus(String driverId, DriverStatus status) async {
    try {
      if (_useRealDatabase) {
        await _realDataService.updateDriverStatus(driverId, status);
      } else {
        await _mockDataService.updateDriverStatus(driverId, status);
      }
    } catch (e) {
      if (_useRealDatabase) {
        print('⚠️ خطأ في قاعدة البيانات الحقيقية، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        await updateDriverStatus(driverId, status);
      } else {
        rethrow;
      }
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // خدمات الطلبات
  // ═══════════════════════════════════════════════════════════════

  Future<String> createOrder(Order order) async {
    try {
      final orderId = _useRealDatabase
          ? await _realDataService.createOrder(order)
          : await _mockDataService.createOrder(order);
      
      return orderId;
    } catch (e) {
      if (_useRealDatabase) {
        print('⚠️ خطأ في قاعدة البيانات الحقيقية، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        return await createOrder(order);
      }
      rethrow;
    }
  }

  Future<List<Order>> getOrdersForCompany(String companyId) async {
    try {
      final orders = _useRealDatabase
          ? await _realDataService.getOrdersForCompany(companyId)
          : await _mockDataService.getOrdersForCompany(companyId);
      
      _ordersController.add(orders);
      return orders;
    } catch (e) {
      if (_useRealDatabase) {
        print('⚠️ خطأ في قاعدة البيانات الحقيقية، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        return await getOrdersForCompany(companyId);
      }
      rethrow;
    }
  }

  Future<List<Order>> getOrdersForDriver(String driverId) async {
    try {
      final orders = _useRealDatabase
          ? await _realDataService.getOrdersForDriver(driverId)
          : await _mockDataService.getOrdersForDriver(driverId);
      
      _ordersController.add(orders);
      return orders;
    } catch (e) {
      if (_useRealDatabase) {
        print('⚠️ خطأ في قاعدة البيانات الحقيقية، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        return await getOrdersForDriver(driverId);
      }
      rethrow;
    }
  }

  Future<List<Order>> getAvailableOrdersForDriver(String driverId) async {
    try {
      final orders = _useRealDatabase
          ? await _realDataService.getAvailableOrdersForDriver(driverId)
          : await _mockDataService.getAvailableOrdersForDriver(driverId);
      
      return orders;
    } catch (e) {
      if (_useRealDatabase) {
        print('⚠️ خطأ في قاعدة البيانات الحقيقية، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        return await getAvailableOrdersForDriver(driverId);
      }
      rethrow;
    }
  }

  Future<void> updateOrderStatus(String orderId, OrderStatus status, {String? note}) async {
    try {
      if (_useRealDatabase) {
        await _realDataService.updateOrderStatus(orderId, status, note: note);
      } else {
        await _mockDataService.updateOrderStatus(orderId, status, note: note);
      }
    } catch (e) {
      if (_useRealDatabase) {
        print('⚠️ خطأ في قاعدة البيانات الحقيقية، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        await updateOrderStatus(orderId, status, note: note);
      } else {
        rethrow;
      }
    }
  }

  Future<void> assignDriverToOrder(String orderId, String driverId) async {
    try {
      if (_useRealDatabase) {
        await _realDataService.assignDriverToOrder(orderId, driverId);
      } else {
        await _mockDataService.assignDriverToOrder(orderId, driverId);
      }
    } catch (e) {
      if (_useRealDatabase) {
        print('⚠️ خطأ في قاعدة البيانات الحقيقية، التحول للبيانات المحاكية');
        _useRealDatabase = false;
        await assignDriverToOrder(orderId, driverId);
      } else {
        rethrow;
      }
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // معلومات النظام
  // ═══════════════════════════════════════════════════════════════

  /// الحصول على نوع قاعدة البيانات المستخدمة
  String get databaseType => _useRealDatabase ? 'Supabase' : 'Mock Data';

  /// التحقق من حالة الاتصال
  bool get isConnected => _useRealDatabase;

  /// تنظيف الموارد
  void dispose() {
    _driversController.close();
    _ordersController.close();
    _userController.close();
    _mockDataService.dispose();
    _realDataService.dispose();
  }
}
