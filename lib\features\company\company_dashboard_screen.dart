import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;
import 'dart:ui';

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_dimensions.dart';
import '../../core/utils/language_manager.dart';
import '../../shared/widgets/company_bottom_navigation.dart';
import 'notifications_screen.dart';
import 'profile_screen.dart';
import 'settings_screen.dart';
import 'order_management_screen.dart';
import 'tracking_screen.dart';

class CompanyDashboardScreen extends StatefulWidget {
  const CompanyDashboardScreen({super.key});

  @override
  State<CompanyDashboardScreen> createState() => _CompanyDashboardScreenState();
}

class _CompanyDashboardScreenState extends State<CompanyDashboardScreen>
    with TickerProviderStateMixin {

  // ═══════════════════════════════════════════════════════════════
  // متحكمات الأنيميشن (Animation Controllers)
  // ═══════════════════════════════════════════════════════════════

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _backgroundController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _backgroundAnimation;

  // ═══════════════════════════════════════════════════════════════
  // بيانات وهمية للعرض (Mock Data)
  // ═══════════════════════════════════════════════════════════════

  final String companyName = "شركة النقل العراقية";
  final String companyLogo = "🏢"; // يمكن استبداله بصورة حقيقية
  final int newOrders = 12;
  final int activeOrders = 8;
  final int completedToday = 25;
  final double monthlyPayments = 15750.0;

  // بيانات السائقين المتوفرين (ضمن 10 كم) - محدثة
  final List<Map<String, dynamic>> availableDrivers = [
    {
      'name': 'أحمد محمد',
      'truckType': 'أتيكو 6 متر',
      'plateNumber': 'ب ج د 1234',
      'vehicleNumber': 'V-001',
      'carModel': 'إيسوزو 2022',
      'distance': '2.5 كم',
      'rating': 4.8,
      'isOnline': true,
      'phone': '07901234567',
      'experience': '5 سنوات',
    },
    {
      'name': 'محمد علي',
      'truckType': 'شاحنة جادر متحرك 13.60م',
      'plateNumber': 'أ ب ج 5678',
      'vehicleNumber': 'V-002',
      'carModel': 'مرسيدس أكتروس 2021',
      'distance': '3.2 كم',
      'rating': 4.6,
      'isOnline': true,
      'phone': '07902345678',
      'experience': '8 سنوات',
    },
    {
      'name': 'علي حسن',
      'truckType': 'أتيكو 10 متر',
      'plateNumber': 'د هـ و 9012',
      'vehicleNumber': 'V-003',
      'carModel': 'فولفو FH 2023',
      'distance': '1.8 كم',
      'rating': 4.9,
      'isOnline': true,
      'phone': '07903456789',
      'experience': '12 سنة',
    },
    {
      'name': 'حسن أحمد',
      'truckType': 'شاحنة طويلة (بارجة) 18-19م',
      'plateNumber': 'ز ح ط 3456',
      'vehicleNumber': 'V-004',
      'carModel': 'سكانيا R450 2022',
      'distance': '4.1 كم',
      'rating': 4.7,
      'isOnline': true,
      'phone': '07904567890',
      'experience': '7 سنوات',
    },
  ];

  // متغير لتتبع عرض جميع السائقين
  bool _showAllDrivers = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    // أنيميشن الظهور التدريجي
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // أنيميشن الانزلاق
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // أنيميشن التكبير
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // أنيميشن الخلفية المتحركة
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.linear,
    ));
  }

  void _startAnimations() {
    _fadeController.forward();
    _backgroundController.repeat();

    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _slideController.forward();
      }
    });

    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) {
        _scaleController.forward();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Scaffold(
          body: AnimatedBuilder(
        animation: _backgroundAnimation,
        builder: (context, child) {
          return Stack(
            children: [
              // الخلفية المتحركة
              _buildAnimatedBackground(),

              // المحتوى الرئيسي
              SafeArea(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // الهيدر الترحيبي
                      // بطاقة ترحيب مصغرة
                      _buildCompactWelcomeHeader(),

                      const SizedBox(height: 24),

                      // البطاقات التفاعلية الثلاثة
                      _buildInteractiveCards(),

                      const SizedBox(height: 28),

                      // قسم السائقين المتوفرين
                      _buildAvailableDriversSection(),

                      const SizedBox(height: 28),

                      // الرسوم البيانية المفيدة
                      _buildUsefulCharts(),



                      const SizedBox(height: 100), // مساحة لشريط التنقل السفلي
                    ],
                  ),
                ),
              ),

              // الزر العائم لإنشاء طلب
              _buildFloatingActionButton(),


            ],
          );
        },
      ),
      bottomNavigationBar: CompanyBottomNavigation(
        currentIndex: 0, // الرئيسية
        onTap: (index) => CompanyBottomNavigation.handleNavigation(context, index),
      ),
    );
      },
    );
  }

  Widget _buildAnimatedBackground() {
    return Positioned.fill(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF0F172A), // أزرق داكن جداً
              Color(0xFF1E3A8A), // أزرق داكن
              Color(0xFF2563EB), // أزرق متوسط
              Color(0xFF3B82F6), // أزرق فاتح
              Color(0xFF60A5FA), // أزرق فاتح جداً
            ],
            stops: [0.0, 0.25, 0.5, 0.75, 1.0],
          ),
        ),
        child: CustomPaint(
          painter: CompanyDashboardBackgroundPainter(_backgroundAnimation.value),
        ),
      ),
    );
  }

  Widget _buildWelcomeHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.primary,
                    Color(0xFF2563EB),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 2,
                ),
                boxShadow: [
                  // ظل خارجي أساسي
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.4),
                    blurRadius: 25,
                    offset: const Offset(0, 12),
                    spreadRadius: 3,
                  ),
                  // إطار لماع داخلي
                  BoxShadow(
                    color: Colors.white.withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, -3),
                    spreadRadius: -2,
                  ),
                  // لمعة جانبية
                  BoxShadow(
                    color: Colors.white.withOpacity(0.1),
                    blurRadius: 15,
                    offset: const Offset(5, 0),
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Row(
                children: [
                  // شعار الشركة
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        companyLogo,
                        style: const TextStyle(fontSize: 30),
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // معلومات الترحيب
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          langManager.getText('أهلاً بك في', 'Welcome to'),
                          style: GoogleFonts.tajawal(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          companyName,
                          style: GoogleFonts.tajawal(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          langManager.getText('لوحة التحكم الرئيسية', 'Main Dashboard'),
                          style: GoogleFonts.tajawal(
                            color: Colors.white.withOpacity(0.8),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // أيقونات الإجراءات
                  Row(
                    children: [
                      // أيقونة الإشعارات
                      _buildHeaderIcon(
                        icon: Bootstrap.bell,
                        onTap: _showNotifications,
                        hasNotification: true,
                      ),
                      const SizedBox(width: 12),
                      // أيقونة الإعدادات
                      _buildHeaderIcon(
                        icon: Bootstrap.gear,
                        onTap: _openSettings,
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeaderIcon({
    required IconData icon,
    required VoidCallback onTap,
    bool hasNotification = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Stack(
          children: [
            Center(
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
            if (hasNotification)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainCards() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return Row(
              children: [
                // عدد الطلبات
                Expanded(
                  child: _buildMainCard(
                    title: langManager.getText('عدد الطلبات', 'Total Orders'),
                    value: newOrders.toString(),
                    icon: Bootstrap.list_ul,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(width: 16),

                // المنفذة
                Expanded(
                  child: _buildMainCard(
                    title: langManager.getText('المنفذة', 'Completed'),
                    value: completedToday.toString(),
                    icon: Bootstrap.check_circle,
                    color: AppColors.success,
                  ),
                ),
                const SizedBox(width: 16),

                // بانتظار التنفيذ
                Expanded(
                  child: _buildMainCard(
                    title: langManager.getText('بانتظار التنفيذ', 'Pending'),
                    value: activeOrders.toString(),
                    icon: Bootstrap.clock,
                    color: AppColors.warning,
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildMainCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            color.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // الأيقونة
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),

          const SizedBox(height: 12),

          // القيمة
          Text(
            value,
            style: GoogleFonts.tajawal(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),

          const SizedBox(height: 4),

          // العنوان
          Text(
            title,
            style: GoogleFonts.tajawal(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildKPICard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    bool hasNotification = false,
  }) {
    return Container(
      height: AppDimensions.kpiCardHeight,
      padding: AppSpacing.compactCardPadding,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            _getCardGradientColor(color),
          ],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          // ظل خارجي ناعم
          BoxShadow(
            color: color.withOpacity(0.15),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 2,
          ),
          // إطار لماع داخلي
          BoxShadow(
            color: Colors.white.withOpacity(0.9),
            blurRadius: 8,
            offset: const Offset(0, -2),
            spreadRadius: -2,
          ),
          // لمعة جانبية
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(4, 0),
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: AppDimensions.iconSizeXL,
                height: AppDimensions.iconSizeXL,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: AppDimensions.iconSizeS,
                ),
              ),
              if (hasNotification)
                Container(
                  width: 6,
                  height: 6,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            value,
            style: GoogleFonts.tajawal(
              fontSize: AppDimensions.fontSizeXL,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.tajawal(
              fontSize: AppDimensions.fontSizeXS,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                langManager.getText('الرسوم البيانية', 'Charts & Analytics'),
                style: GoogleFonts.tajawal(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),

              // مخطط نشاط الطلبات
              _buildChartCard(
                title: langManager.getText('نشاط الطلبات (آخر 30 يوم)', 'Orders Activity (Last 30 Days)'),
                child: _buildOrdersChart(),
              ),

              const SizedBox(height: 16),

              Row(
                children: [
                  // توزيع أنواع الحمولات
                  Expanded(
                    child: _buildChartCard(
                      title: langManager.getText('أنواع الحمولات', 'Cargo Types'),
                      child: _buildCargoTypesChart(),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // أداء السائقين
                  Expanded(
                    child: _buildChartCard(
                      title: langManager.getText('أداء السائقين', 'Drivers Performance'),
                      child: _buildDriversChart(),
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildChartCard({
    required String title,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Color(0xFFF0F9FF), // أزرق فاتح جداً
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.8),
          width: 2,
        ),
        boxShadow: [
          // ظل خارجي ناعم
          BoxShadow(
            color: AppColors.primary.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 2,
          ),
          // إطار لماع داخلي
          BoxShadow(
            color: Colors.white.withOpacity(0.9),
            blurRadius: 8,
            offset: const Offset(0, -2),
            spreadRadius: -2,
          ),
          // لمعة جانبية
          BoxShadow(
            color: Colors.blue.withOpacity(0.05),
            blurRadius: 20,
            offset: const Offset(4, 0),
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.tajawal(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildOrdersChart() {
    // بيانات ثابتة لتجنب الحركة السريعة
    final List<double> chartData = [45, 65, 30, 80, 55, 70, 40];

    return Container(
      height: 120,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: List.generate(7, (index) {
          final height = 20.0 + chartData[index];
          return AnimatedContainer(
            duration: Duration(milliseconds: 800 + (index * 100)),
            curve: Curves.easeOutCubic,
            width: 20,
            height: height,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.bottomCenter,
                end: Alignment.topCenter,
                colors: [
                  AppColors.primary,
                  AppColors.primary.withOpacity(0.6),
                ],
              ),
              borderRadius: BorderRadius.circular(4),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildCargoTypesChart() {
    return Container(
      height: 100,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // دائرة بيانية بسيطة
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: SweepGradient(
                colors: [
                  AppColors.primary,
                  AppColors.secondary,
                  AppColors.warning,
                  AppColors.success,
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDriversChart() {
    return Container(
      height: 100,
      child: Column(
        children: List.generate(3, (index) {
          final percentage = 0.6 + (index * 0.15);
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              children: [
                Text(
                  'سائق ${index + 1}',
                  style: GoogleFonts.tajawal(fontSize: 12),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: LinearProgressIndicator(
                    value: percentage,
                    backgroundColor: Colors.grey[200],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      [AppColors.success, AppColors.warning, AppColors.info][index],
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return Positioned(
      bottom: 70, // فوق الشريط السفلي بقليل
      right: 20,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Consumer<LanguageManager>(
          builder: (context, langManager, child) {
            return Container(
              height: 56,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.primary,
                    Color(0xFF2563EB),
                  ],
                ),
                borderRadius: BorderRadius.circular(28),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.4),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                    spreadRadius: 2,
                  ),
                  BoxShadow(
                    color: Colors.white.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                    spreadRadius: -1,
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: _createNewOrder,
                  borderRadius: BorderRadius.circular(28),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Icon(
                          Bootstrap.plus_lg,
                          color: Colors.white,
                          size: 18,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        langManager.getText('طلب جديد', 'New Order'),
                        style: GoogleFonts.tajawal(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildQuickAccessCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        splashColor: color.withOpacity(0.1),
        highlightColor: color.withOpacity(0.05),
        child: Container(
          height: AppDimensions.quickAccessCardHeight,
          padding: AppSpacing.compactCardPadding,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                _getQuickAccessGradientColor(color),
              ],
            ),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: color.withOpacity(0.2),
              width: 1,
            ),
            boxShadow: [
              // ظل خارجي ملون محسن
              BoxShadow(
                color: color.withOpacity(0.2),
                blurRadius: 15,
                offset: const Offset(0, 8),
                spreadRadius: 2,
              ),
              // إطار لماع داخلي قوي
              BoxShadow(
                color: Colors.white.withOpacity(0.9),
                blurRadius: 8,
                offset: const Offset(0, -2),
                spreadRadius: -1,
              ),
              // لمعة جانبية ذهبية
              BoxShadow(
                color: const Color(0xFFFBBF24).withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(3, 3),
                spreadRadius: 1,
              ),
              // توهج ناعم
              BoxShadow(
                color: color.withOpacity(0.05),
                blurRadius: 25,
                offset: const Offset(0, 0),
                spreadRadius: 3,
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: AppDimensions.iconSizeXL,
                height: AppDimensions.iconSizeXL,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: AppDimensions.iconSizeS,
                ),
              ),
              const SizedBox(height: AppDimensions.paddingXS),
              Text(
                title,
                textAlign: TextAlign.center,
                style: GoogleFonts.tajawal(
                  fontSize: AppDimensions.fontSizeXS,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentActivitiesSection() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    langManager.getText('آخر الأنشطة', 'Recent Activities'),
                    style: GoogleFonts.tajawal(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  TextButton(
                    onPressed: _viewAllActivities,
                    child: Text(
                      langManager.getText('عرض الكل', 'View All'),
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      Color(0xFFF0F9FF), // أزرق كريستالي فاتح
                      Color(0xFFFEF3C7), // برتقالي ذهبي فاتح
                    ],
                    stops: [0.0, 0.6, 1.0],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.8),
                    width: 2,
                  ),
                  boxShadow: [
                    // ظل خارجي متدرج
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.15),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                      spreadRadius: 3,
                    ),
                    // إطار لماع داخلي قوي
                    BoxShadow(
                      color: Colors.white.withOpacity(0.95),
                      blurRadius: 10,
                      offset: const Offset(0, -3),
                      spreadRadius: -2,
                    ),
                    // لمعة ذهبية جانبية
                    BoxShadow(
                      color: const Color(0xFFFBBF24).withOpacity(0.1),
                      blurRadius: 25,
                      offset: const Offset(5, 5),
                      spreadRadius: 2,
                    ),
                    // توهج أزرق ناعم
                    BoxShadow(
                      color: AppColors.info.withOpacity(0.08),
                      blurRadius: 30,
                      offset: const Offset(-3, -3),
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    _buildActivityItem(
                      title: langManager.getText('طلب جديد تم إنشاؤه', 'New order created'),
                      subtitle: langManager.getText('طلب #12345 - نقل بضائع', 'Order #12345 - Cargo transport'),
                      time: langManager.getText('منذ 5 دقائق', '5 minutes ago'),
                      icon: Bootstrap.plus_circle,
                      color: AppColors.success,
                    ),
                    _buildActivityItem(
                      title: langManager.getText('تم تعيين سائق', 'Driver assigned'),
                      subtitle: langManager.getText('أحمد محمد - طلب #12344', 'Ahmed Mohammed - Order #12344'),
                      time: langManager.getText('منذ 15 دقيقة', '15 minutes ago'),
                      icon: Bootstrap.person_check,
                      color: AppColors.info,
                    ),
                    _buildActivityItem(
                      title: langManager.getText('تم إكمال التسليم', 'Delivery completed'),
                      subtitle: langManager.getText('طلب #12343 - تم بنجاح', 'Order #12343 - Completed successfully'),
                      time: langManager.getText('منذ ساعة', '1 hour ago'),
                      icon: Bootstrap.check_circle,
                      color: AppColors.success,
                      isLast: true,
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildActivityItem({
    required String title,
    required String subtitle,
    required String time,
    required IconData icon,
    required Color color,
    bool isLast = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: isLast ? null : Border(
          bottom: BorderSide(
            color: Colors.grey.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: GoogleFonts.tajawal(
              fontSize: 11,
              fontWeight: FontWeight.w400,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }





  // ═══════════════════════════════════════════════════════════════
  // دوال مساعدة (Helper Functions)
  // ═══════════════════════════════════════════════════════════════

  Color _getCardGradientColor(Color originalColor) {
    // تحديد ألوان التدرج بناءً على اللون الأصلي
    if (originalColor == AppColors.warning) {
      // برتقالي فاتح للطلبات الجديدة
      return const Color(0xFFFED7AA);
    } else if (originalColor == AppColors.info) {
      // أزرق فاتح للطلبات النشطة
      return const Color(0xFFBFDBFE);
    } else if (originalColor == AppColors.success) {
      // برتقالي خوخي للمكتملة
      return const Color(0xFFFFE4B5);
    } else if (originalColor == AppColors.primary) {
      // أزرق فاتح جداً للمدفوعات
      return const Color(0xFFE0F2FE);
    } else {
      // لون افتراضي
      return originalColor.withOpacity(0.1);
    }
  }

  Color _getQuickAccessGradientColor(Color originalColor) {
    // استخدام نفس لون كارت الهيدر لجميع كروت الوصول السريع
    return const Color(0xFF2563EB).withOpacity(0.1);
  }

  // ═══════════════════════════════════════════════════════════════
  // دوال الإجراءات (Action Functions)
  // ═══════════════════════════════════════════════════════════════

  void _showNotifications() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const NotificationsScreen(),
      ),
    );
  }



  void _createNewOrder() {
    // الانتقال إلى شاشة إنشاء طلب جديد
    Navigator.pushNamed(context, '/create-order');
  }





  void _openSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SettingsScreen(),
      ),
    );
  }

  void _viewAllActivities() {
    // إنشاء شاشة جميع الأنشطة مؤقتة
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => _buildAllActivitiesScreen(),
      ),
    );
  }

  Widget _buildAllActivitiesScreen() {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              langManager.getText('جميع الأنشطة', 'All Activities'),
              style: GoogleFonts.tajawal(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            backgroundColor: AppColors.primary,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.white),
          ),
          body: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: 20, // عدد الأنشطة الوهمية
            itemBuilder: (context, index) {
              final activities = [
                {
                  'title': langManager.getText('طلب جديد تم إنشاؤه', 'New order created'),
                  'subtitle': langManager.getText('طلب #${12345 + index} - نقل بضائع', 'Order #${12345 + index} - Cargo transport'),
                  'time': langManager.getText('منذ ${index + 1} دقيقة', '${index + 1} minutes ago'),
                  'icon': Bootstrap.plus_circle,
                  'color': AppColors.success,
                },
                {
                  'title': langManager.getText('تم تعيين سائق', 'Driver assigned'),
                  'subtitle': langManager.getText('أحمد محمد - طلب #${12344 + index}', 'Ahmed Mohammed - Order #${12344 + index}'),
                  'time': langManager.getText('منذ ${(index + 1) * 5} دقيقة', '${(index + 1) * 5} minutes ago'),
                  'icon': Bootstrap.person_check,
                  'color': AppColors.info,
                },
                {
                  'title': langManager.getText('تم إكمال التسليم', 'Delivery completed'),
                  'subtitle': langManager.getText('طلب #${12343 + index} - تم بنجاح', 'Order #${12343 + index} - Completed successfully'),
                  'time': langManager.getText('منذ ${index + 1} ساعة', '${index + 1} hour ago'),
                  'icon': Bootstrap.check_circle,
                  'color': AppColors.success,
                },
              ];

              final activity = activities[index % activities.length];

              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: activity['color'] as Color,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        activity['icon'] as IconData,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            activity['title'] as String,
                            style: GoogleFonts.tajawal(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            activity['subtitle'] as String,
                            style: GoogleFonts.tajawal(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      activity['time'] as String,
                      style: GoogleFonts.tajawal(
                        fontSize: 11,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  // دوال التنقل للبطاقات التفاعلية
  void _navigateToNewOrders() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const OrderManagementScreen(),
      ),
    );
  }

  void _navigateToInProgressOrders() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const TrackingScreen(),
      ),
    );
  }

  void _navigateToCompletedOrders() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const OrderManagementScreen(),
      ),
    );
  }

  // دالة إرسال طلب للسائق
  void _sendRequestToDriver(Map<String, dynamic> driver) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          Provider.of<LanguageManager>(context, listen: false)
              .getText('تم إرسال الطلب إلى ${driver['name']}', 'Request sent to ${driver['name']}'),
          style: GoogleFonts.tajawal(),
        ),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // دالة عرض تفاصيل السائق
  void _showDriverDetails(Map<String, dynamic> driver, LanguageManager langManager) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1F2937),
              Color(0xFF111827),
            ],
          ),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: const Color(0xFF6B7280),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // محتوى التفاصيل
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // رأس التفاصيل
                    Row(
                      children: [
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF3B82F6), Color(0xFF1D4ED8)],
                            ),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: const Icon(
                            Bootstrap.person_fill,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                driver['name'] ?? 'غير محدد',
                                style: GoogleFonts.tajawal(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  const Icon(Icons.star, color: Color(0xFFFBBF24), size: 16),
                                  const SizedBox(width: 4),
                                  Text(
                                    (driver['rating'] ?? 0.0).toString(),
                                    style: GoogleFonts.tajawal(
                                      fontSize: 14,
                                      color: const Color(0xFF9CA3AF),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // تفاصيل السائق
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            _buildDetailRow(Bootstrap.truck, 'نوع المركبة', driver['truckType'] ?? 'غير محدد', langManager),
                            _buildDetailRow(Bootstrap.hash, 'رقم السيارة', driver['vehicleNumber'] ?? 'غير محدد', langManager),
                            _buildDetailRow(Bootstrap.car_front, 'موديل السيارة', driver['carModel'] ?? 'غير محدد', langManager),
                            _buildDetailRow(Bootstrap.credit_card, 'رقم اللوحة', driver['plateNumber'] ?? 'غير محدد', langManager),
                            _buildDetailRow(Bootstrap.geo_alt_fill, 'المسافة', driver['distance'] ?? 'غير محدد', langManager),
                            _buildDetailRow(Bootstrap.telephone, 'رقم الهاتف', driver['phone'] ?? 'غير محدد', langManager),
                            _buildDetailRow(Bootstrap.award, 'سنوات الخبرة', driver['experience'] ?? 'غير محدد', langManager),
                          ],
                        ),
                      ),
                    ),

                    // زر إرسال الطلب
                    Container(
                      width: double.infinity,
                      height: 50,
                      margin: const EdgeInsets.only(top: 16),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF3B82F6), Color(0xFF1D4ED8)],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF3B82F6).withOpacity(0.4),
                            blurRadius: 12,
                            offset: const Offset(0, 6),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            Navigator.pop(context);
                            _sendRequestToDriver(driver);
                          },
                          borderRadius: BorderRadius.circular(16),
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Bootstrap.send, color: Colors.white, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  langManager.getText('إرسال طلب', 'Send Request'),
                                  style: GoogleFonts.tajawal(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value, LanguageManager langManager) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF4B5563),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF3B82F6).withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: const Color(0xFF3B82F6),
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: const Color(0xFF9CA3AF),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDriverCard(Map<String, dynamic> driver, LanguageManager langManager, bool isLast) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1F2937), // رمادي داكن
            Color(0xFF374151), // رمادي متوسط
            Color(0xFF4B5563), // رمادي فاتح
          ],
          stops: [0.0, 0.5, 1.0],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF6B7280).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: const Color(0xFF374151).withOpacity(0.5),
            blurRadius: 6,
            offset: const Offset(0, -1),
            spreadRadius: -1,
          ),
        ],
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // أيقونة السائق
            Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF3B82F6),
                    Color(0xFF1D4ED8),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF3B82F6).withOpacity(0.4),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Bootstrap.person_fill,
                color: Colors.white,
                size: 20,
              ),
            ),

            const SizedBox(width: 16),

            // معلومات السائق (اسم + نوع الشاحنة)
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اسم السائق
                  Text(
                    driver['name'] ?? 'غير محدد',
                    style: GoogleFonts.tajawal(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),

                  const SizedBox(height: 4),

                  // نوع الشاحنة
                  Text(
                    driver['truckType'] ?? 'غير محدد',
                    style: GoogleFonts.tajawal(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF9CA3AF),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            const SizedBox(width: 12),

            // العمود الأيمن: المسافة + زر الطلب
            Column(
              children: [
                // المسافة
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF10B981), Color(0xFF059669)],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF10B981).withOpacity(0.3),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    driver['distance'] ?? '0 كم',
                    style: GoogleFonts.tajawal(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),

                const SizedBox(height: 8),

                // زر طلب منظم
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => _sendRequestToDriver(driver),
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF3B82F6), Color(0xFF1D4ED8)],
                        ),
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF3B82F6).withOpacity(0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        langManager.getText('طلب', 'Request'),
                        style: GoogleFonts.tajawal(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }



  // بطاقة ترحيب مكبرة
  Widget _buildCompactWelcomeHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF1E3A8A),
                  Color(0xFF2563EB),
                  Color(0xFF3B82F6),
                ],
                stops: [0.0, 0.6, 1.0],
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF2563EB).withOpacity(0.4),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                  spreadRadius: 3,
                ),
                BoxShadow(
                  color: Colors.white.withOpacity(0.1),
                  blurRadius: 12,
                  offset: const Offset(0, -3),
                ),
              ],
            ),
            child: Column(
              children: [
                // الصف الأول - الترحيب والإشعارات
                Row(
                  children: [
                    // أيقونة الشركة
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 2,
                        ),
                      ),
                      child: const Icon(
                        Bootstrap.building,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),

                    const SizedBox(width: 16),

                    // النصوص
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            langManager.getText('أهلاً بك', 'Welcome'),
                            style: GoogleFonts.tajawal(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            langManager.getText('شركة النقل المتميزة', 'Premium Transport Company'),
                            style: GoogleFonts.tajawal(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            langManager.getText('لوحة التحكم الرئيسية', 'Main Dashboard'),
                            style: GoogleFonts.tajawal(
                              fontSize: 12,
                              color: Colors.white60,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // زر الإشعارات
                    GestureDetector(
                      onTap: _showNotifications,
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(14),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Stack(
                          children: [
                            const Center(
                              child: Icon(
                                Bootstrap.bell,
                                color: Colors.white,
                                size: 22,
                              ),
                            ),
                            // نقطة الإشعار
                            Positioned(
                              top: 10,
                              right: 10,
                              child: Container(
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 1,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),


              ],
            ),
          );
        },
      ),
    );
  }

  // قسم السائقين المتوفرين
  Widget _buildAvailableDriversSection() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان القسم
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    langManager.getText('متوفر', 'Available'),
                    style: GoogleFonts.tajawal(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF1E3A8A), Color(0xFF3B82F6)],
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      langManager.getText('ضمن 10 كم', 'Within 10 km'),
                      style: GoogleFonts.tajawal(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // قائمة السائقين
              Container(
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF111827), // رمادي داكن جداً
                      Color(0xFF1F2937), // رمادي داكن
                      Color(0xFF374151), // رمادي متوسط
                    ],
                    stops: [0.0, 0.5, 1.0],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: const Color(0xFF4B5563).withOpacity(0.5),
                    width: 2,
                  ),
                  boxShadow: [
                    // ظل خارجي داكن
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                      spreadRadius: 3,
                    ),
                    // إطار لماع داخلي
                    BoxShadow(
                      color: const Color(0xFF374151).withOpacity(0.8),
                      blurRadius: 10,
                      offset: const Offset(0, -3),
                      spreadRadius: -2,
                    ),
                    // لمعة جانبية زرقاء
                    BoxShadow(
                      color: const Color(0xFF3B82F6).withOpacity(0.1),
                      blurRadius: 25,
                      offset: const Offset(5, 5),
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // عرض السائقين (3 أو جميع)
                    ...(_showAllDrivers ? availableDrivers : availableDrivers.take(3))
                        .toList()
                        .asMap()
                        .entries
                        .map((entry) {
                      final index = entry.key;
                      final driver = entry.value;
                      final driversToShow = _showAllDrivers ? availableDrivers : availableDrivers.take(3).toList();
                      final isLast = index == driversToShow.length - 1 && _showAllDrivers;

                      return _buildDriverCard(driver, langManager, isLast);
                    }).toList(),

                    // زر عرض الجميع/إخفاء
                    if (availableDrivers.length > 3)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border(
                            top: BorderSide(
                              color: const Color(0xFF4B5563).withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                        ),
                        child: Center(
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  _showAllDrivers = !_showAllDrivers;
                                });
                              },
                              borderRadius: BorderRadius.circular(25),
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    colors: [Color(0xFF1E3A8A), Color(0xFF3B82F6)],
                                  ),
                                  borderRadius: BorderRadius.circular(25),
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(0xFF3B82F6).withOpacity(0.3),
                                      blurRadius: 10,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      _showAllDrivers ? Bootstrap.chevron_up : Bootstrap.chevron_down,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      _showAllDrivers
                                          ? langManager.getText('إخفاء', 'Hide')
                                          : langManager.getText('عرض الجميع (${availableDrivers.length})', 'Show All (${availableDrivers.length})'),
                                      style: GoogleFonts.tajawal(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // البطاقات التفاعلية الثلاثة
  Widget _buildInteractiveCards() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Column(
            children: [
              // الطلبات الجديدة
              _buildInteractiveCard(
                title: langManager.getText('طلبات جديدة', 'New Orders'),
                value: '8',
                icon: Bootstrap.box,
                gradientColors: const [
                  Color(0xFF1A73E8), // بداية داكنة
                  Color(0xFF4A90E2), // وسط
                  Color(0xFF80C8FF), // فاتحة
                  Color(0xFFB3DAFF), // نهاية فاتحة
                ],
                onTap: () => _navigateToNewOrders(),
              ),
              const SizedBox(height: 16),

              // قيد التنفيذ
              _buildInteractiveCard(
                title: langManager.getText('قيد التنفيذ', 'In Progress'),
                value: '5',
                icon: Bootstrap.truck,
                gradientColors: const [
                  Color(0xFFFF6F00), // بداية داكنة
                  Color(0xFFFF8F33), // وسط
                  Color(0xFFFFB46C), // فاتحة
                  Color(0xFFFFD4A3), // نهاية فاتحة
                ],
                onTap: () => _navigateToInProgressOrders(),
              ),
              const SizedBox(height: 16),

              // مكتملة
              _buildInteractiveCard(
                title: langManager.getText('مكتملة', 'Completed'),
                value: '12',
                icon: Bootstrap.check_circle_fill,
                gradientColors: const [
                  Color(0xFF2E7D32), // بداية داكنة
                  Color(0xFF4CAF50), // وسط
                  Color(0xFF8EE5A2), // فاتحة
                  Color(0xFFB8F2C4), // نهاية فاتحة
                ],
                onTap: () => _navigateToCompletedOrders(),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildInteractiveCard({
    required String title,
    required String value,
    required IconData icon,
    required List<Color> gradientColors,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            width: double.infinity,
            height: 80, // تصغير الارتفاع من 100 إلى 80
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
                colors: gradientColors,
              ),
              borderRadius: BorderRadius.circular(12), // تصغير الحواف من 16 إلى 12
              boxShadow: [
                BoxShadow(
                  color: gradientColors.last.withOpacity(0.2), // تقليل الظل
                  blurRadius: 8, // تقليل الضبابية
                  offset: const Offset(0, 4), // تقليل الإزاحة
                  spreadRadius: 1, // تقليل الانتشار
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16), // تقليل الحشو من 20 إلى 16
              child: Row(
                children: [
                  // الأيقونة
                  Container(
                    width: 40, // تصغير من 50 إلى 40
                    height: 40, // تصغير من 50 إلى 40
                    decoration: BoxDecoration(
                      color: gradientColors.last.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(10), // تصغير من 12 إلى 10
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: 22, // تصغير من 28 إلى 22
                    ),
                  ),

                  const SizedBox(width: 12), // تقليل المسافة من 16 إلى 12

                  // النصوص
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Flexible(
                          child: Text(
                            title,
                            style: GoogleFonts.tajawal(
                              fontSize: 14, // تصغير من 16 إلى 14
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(height: 2), // تقليل من 4 إلى 2
                        Flexible(
                          child: Text(
                            value,
                            style: GoogleFonts.tajawal(
                              fontSize: 24, // تصغير من 28 إلى 24
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // سهم التنقل
                  Container(
                    width: 28, // تصغير من 32 إلى 28
                    height: 28, // تصغير من 32 إلى 28
                    decoration: BoxDecoration(
                      color: gradientColors.last.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(6), // تصغير من 8 إلى 6
                    ),
                    child: const Icon(
                      Bootstrap.chevron_left,
                      color: Colors.white,
                      size: 14, // تصغير من 16 إلى 14
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // الرسوم البيانية المفيدة
  Widget _buildUsefulCharts() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                langManager.getText('التحليلات', 'Analytics'),
                style: GoogleFonts.tajawal(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),

              Row(
                children: [
                  // رسم دائري - توزيع الطلبات
                  Expanded(
                    child: _buildPieChart(langManager),
                  ),
                  const SizedBox(width: 16),

                  // رسم شريطي - طلبات الأسبوع
                  Expanded(
                    child: _buildBarChart(langManager),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPieChart(LanguageManager langManager) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 180, // تصغير الارتفاع من 200 إلى 180
      padding: const EdgeInsets.all(14), // تقليل الحشو من 16 إلى 14
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937), // دائماً داكن
        borderRadius: BorderRadius.circular(12), // تصغير من 16 إلى 12
        border: Border.all(
          color: Colors.white.withOpacity(0.1), // دائماً حدود داكنة
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3), // دائماً ظل داكن
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            langManager.getText('توزيع الطلبات', 'Orders Distribution'),
            style: GoogleFonts.tajawal(
              fontSize: 13, // تصغير من 14 إلى 13
              fontWeight: FontWeight.w600,
              color: Colors.white, // دائماً أبيض
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Row(
              children: [
                // رسم دائري مبسط
                Expanded(
                  flex: 2,
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: SweepGradient(
                        colors: [
                          const Color(0xFF3B82F6), // أزرق - جديدة
                          const Color(0xFFF59E0B), // أصفر - قيد التنفيذ
                          const Color(0xFF10B981), // أخضر - مكتملة
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // المؤشرات
                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildLegendItem('جديدة', '32%', const Color(0xFF3B82F6)),
                      const SizedBox(height: 8),
                      _buildLegendItem('قيد التنفيذ', '20%', const Color(0xFFF59E0B)),
                      const SizedBox(height: 8),
                      _buildLegendItem('مكتملة', '48%', const Color(0xFF10B981)),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarChart(LanguageManager langManager) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 180, // تصغير الارتفاع من 220 إلى 180
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937), // دائماً داكن
        borderRadius: BorderRadius.circular(12), // تصغير من 16 إلى 12
        border: Border.all(
          color: Colors.white.withOpacity(0.1), // دائماً حدود داكنة
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3), // دائماً ظل داكن
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            langManager.getText('طلبات الأسبوع', 'Weekly Orders'),
            style: GoogleFonts.tajawal(
              fontSize: 13, // تصغير من 14 إلى 13
              fontWeight: FontWeight.w600,
              color: Colors.white, // دائماً أبيض
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Flexible(child: _buildBar('س', 12, 50, const Color(0xFF3B82F6))),
                    Flexible(child: _buildBar('ح', 8, 35, const Color(0xFF8B5CF6))),
                    Flexible(child: _buildBar('ن', 15, 65, const Color(0xFF10B981))),
                    Flexible(child: _buildBar('ث', 10, 45, const Color(0xFFF59E0B))),
                    Flexible(child: _buildBar('ر', 18, 75, const Color(0xFFEF4444))),
                    Flexible(child: _buildBar('خ', 14, 60, const Color(0xFF06B6D4))),
                    Flexible(child: _buildBar('ج', 20, 85, const Color(0xFF84CC16))),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, String percentage, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            '$label $percentage',
            style: GoogleFonts.tajawal(
              fontSize: 11, // تصغير من 12 إلى 11
              color: Colors.grey[300], // دائماً رمادي فاتح للوضع الداكن
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBar(String day, int orders, double height, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            orders.toString(),
            style: GoogleFonts.tajawal(
              fontSize: 9, // تصغير من 10 إلى 9
              fontWeight: FontWeight.bold,
              color: Colors.grey[200], // دائماً رمادي فاتح للوضع الداكن
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: 20,
            height: height,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.bottomCenter,
                end: Alignment.topCenter,
                colors: [
                  color,
                  color.withOpacity(0.7),
                ],
              ),
              borderRadius: BorderRadius.circular(4),
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.2),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            day,
            style: GoogleFonts.tajawal(
              fontSize: 9, // تصغير من 10 إلى 9
              fontWeight: FontWeight.w600,
              color: Colors.grey[300], // دائماً رمادي فاتح للوضع الداكن
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactChart() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.primary.withOpacity(0.1),
            AppColors.primary.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          'مخطط بياني مصغر',
          style: GoogleFonts.tajawal(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ),
    );
  }
}

// ═══════════════════════════════════════════════════════════════
// كلاس رسم الخلفية المتحركة (Animated Background Painter)
// ═══════════════════════════════════════════════════════════════

class CompanyDashboardBackgroundPainter extends CustomPainter {
  final double animationValue;

  CompanyDashboardBackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.withOpacity(0.03)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // رسم شبكة متحركة
    final gridSize = 40.0;
    final offset = animationValue * gridSize;

    // خطوط عمودية
    for (double x = -gridSize + (offset % gridSize); x < size.width + gridSize; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // خطوط أفقية
    for (double y = -gridSize + (offset % gridSize); y < size.height + gridSize; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // رسم دوائر متحركة
    final circlePaint = Paint()
      ..color = AppColors.primary.withOpacity(0.02)
      ..style = PaintingStyle.fill;

    final circleCount = 5;
    for (int i = 0; i < circleCount; i++) {
      final angle = (animationValue * 2 * math.pi) + (i * 2 * math.pi / circleCount);
      final radius = 100.0 + (i * 20);
      final centerX = size.width / 2 + math.cos(angle) * radius;
      final centerY = size.height / 2 + math.sin(angle) * radius;

      canvas.drawCircle(
        Offset(centerX, centerY),
        20.0 + (i * 5),
        circlePaint,
      );
    }

    // رسم مربعات دوارة
    final squarePaint = Paint()
      ..color = AppColors.secondary.withOpacity(0.02)
      ..style = PaintingStyle.fill;

    canvas.save();
    canvas.translate(size.width * 0.8, size.height * 0.2);
    canvas.rotate(animationValue * 2 * math.pi);
    canvas.drawRect(
      const Rect.fromLTWH(-15, -15, 30, 30),
      squarePaint,
    );
    canvas.restore();

    canvas.save();
    canvas.translate(size.width * 0.2, size.height * 0.8);
    canvas.rotate(-animationValue * 2 * math.pi);
    canvas.drawRect(
      const Rect.fromLTWH(-20, -20, 40, 40),
      squarePaint,
    );
    canvas.restore();
  }

  @override
  bool shouldRepaint(CompanyDashboardBackgroundPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}