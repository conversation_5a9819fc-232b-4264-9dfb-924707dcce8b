import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'core/theme/app_theme.dart';

import 'core/utils/language_manager.dart';

// المزودين الجدد
import 'shared/providers/auth_provider.dart';
import 'shared/providers/order_provider.dart';
import 'shared/providers/driver_provider.dart';
import 'shared/services/database_service.dart';
import 'shared/services/notification_service.dart';
import 'shared/services/supabase_service.dart';

import 'features/splash/splash_screen.dart';
import 'features/auth/driver_login_screen.dart';
import 'features/auth/driver_register_screen.dart';
import 'features/auth/company_login_screen.dart';
import 'features/auth/company_register_screen.dart';
import 'features/company/company_dashboard_screen.dart';
import 'features/company/create_order_screen.dart';
import 'features/company/order_management_screen.dart';
import 'features/company/tracking_screen.dart';
import 'features/company/analytics_screen.dart';

import 'features/about/about_screen.dart';
import 'features/dashboard/driver_dashboard_screen.dart';
import 'features/orders/orders_screen.dart';
import 'features/map/map_screen.dart';
import 'features/settings/settings_screen.dart';

// شاشات السائقين الإضافية
import 'features/ratings/ratings_screen.dart';
import 'features/statistics/statistics_screen.dart';
import 'features/vehicle/vehicle_management_screen.dart';
import 'features/wallet/wallet_screen.dart';
import 'features/earnings/earnings_screen.dart';
import 'features/profile/profile_screen.dart';
import 'features/support/support_screen.dart';
import 'features/notifications/notifications_screen.dart';

// شاشات الشركات الإضافية
import 'features/company/company_profile_screen.dart';
import 'features/company/notifications_screen.dart' as company_notifications;
import 'features/company/settings_screen.dart' as company_settings;

// شاشات الطلبات الإضافية
import 'features/orders/create_order_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تعيين اتجاه الشاشة
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // تعيين لون شريط الحالة
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // إنشاء مدير اللغة وتحميل اللغة المحفوظة
  final languageManager = LanguageManager();
  await languageManager.loadSavedLanguage();

  // تهيئة Supabase
  await SupabaseService.initialize();

  // تهيئة خدمة قاعدة البيانات الموحدة
  await DatabaseService().initialize();

  // تهيئة خدمة الإشعارات
  await NotificationService().initializeMockNotifications();

  runApp(HamoolatiDriverApp(languageManager: languageManager));
}

class HamoolatiDriverApp extends StatelessWidget {
  final LanguageManager languageManager;

  const HamoolatiDriverApp({super.key, required this.languageManager});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: languageManager),
        ChangeNotifierProvider(create: (_) => AuthProvider()..initialize()),
        ChangeNotifierProvider(create: (_) => OrderProvider()..initialize()),
        ChangeNotifierProvider(create: (_) => DriverProvider()..initialize()),
      ],
      child: Consumer<LanguageManager>(
        builder: (context, langManager, child) {
          return MaterialApp(
            title: AppTexts.appName(langManager),
            debugShowCheckedModeBanner: false,

            // الثيمات
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,

            // دعم اللغة العربية والإنجليزية
            locale: langManager.currentLocale,
            supportedLocales: const [
              Locale('ar', 'IQ'), // العربية
              Locale('en', 'US'), // الإنجليزية
            ],
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],

            // الشاشة الرئيسية
            home: const SplashScreen(),

            // المسارات
            routes: {
              '/splash': (context) => const SplashScreen(),
              '/driver-login': (context) => const DriverLoginScreen(),
              '/driver-register': (context) => const DriverRegisterScreen(),
              '/company-login': (context) => const CompanyLoginScreen(),
              '/company-register': (context) => const CompanyRegisterScreen(),
              '/company-dashboard': (context) => const CompanyDashboardScreen(),
              '/create-order': (context) => const CreateOrderScreen(),
              '/order-management': (context) => const OrderManagementScreen(),
              '/tracking': (context) => const TrackingScreen(),
              '/analytics': (context) => const AnalyticsScreen(),

              '/about': (context) => const AboutScreen(),
              '/dashboard': (context) => const DriverDashboardScreen(),
              '/orders': (context) => const OrdersScreen(),
              '/map': (context) => const MapScreen(),
              '/settings': (context) => const SettingsScreen(),

              // مسارات السائقين الإضافية
              '/ratings': (context) => const RatingsScreen(),
              '/statistics': (context) => const StatisticsScreen(),
              '/vehicle-management': (context) => const VehicleManagementScreen(),
              '/wallet': (context) => const WalletScreen(),
              '/earnings': (context) => const EarningsScreen(),
              '/profile': (context) => const ProfileScreen(),
              '/support': (context) => const SupportScreen(),
              '/notifications': (context) => const NotificationsScreen(),

              // مسارات الشركات الإضافية
              '/company-profile': (context) => const CompanyProfileScreen(),
              '/company-notifications': (context) => const company_notifications.NotificationsScreen(),
              '/company-settings': (context) => const company_settings.SettingsScreen(),

              // مسارات الطلبات الإضافية
              '/create-order': (context) => const CreateOrderScreen(),
            },

            // معالج المسارات غير المعرفة
            onUnknownRoute: (settings) {
              return MaterialPageRoute(
                builder: (context) => const SplashScreen(),
              );
            },
          );
        },
      ),
    );
  }
}
