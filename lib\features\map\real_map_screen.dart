import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:icons_plus/icons_plus.dart';
import 'dart:async';

import '../../core/constants/app_colors.dart';
import '../../core/utils/language_manager.dart';
import '../../core/services/location_service.dart';

class RealMapScreen extends StatefulWidget {
  const RealMapScreen({super.key});

  @override
  State<RealMapScreen> createState() => _RealMapScreenState();
}

class _RealMapScreenState extends State<RealMapScreen> {
  GoogleMapController? _mapController;
  Position? _currentPosition;
  bool _isLoading = true;
  String _errorMessage = '';
  
  // إعدادات الخريطة
  static const CameraPosition _initialPosition = CameraPosition(
    target: LatLng(33.3152, 44.3661), // بغداد
    zoom: 14.0,
  );
  
  // Markers
  final Set<Marker> _markers = {};
  final Set<Polyline> _polylines = {};
  
  @override
  void initState() {
    super.initState();
    _initializeMap();
  }
  
  Future<void> _initializeMap() async {
    try {
      // طلب صلاحيات الموقع
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() {
          _errorMessage = 'خدمة الموقع غير مفعلة';
          _isLoading = false;
        });
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _errorMessage = 'تم رفض صلاحية الوصول للموقع';
            _isLoading = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _errorMessage = 'صلاحية الموقع مرفوضة نهائياً';
          _isLoading = false;
        });
        return;
      }

      // الحصول على الموقع الحالي
      _currentPosition = await Geolocator.getCurrentPosition();
      
      // إضافة marker للموقع الحالي
      _addCurrentLocationMarker();
      
      setState(() {
        _isLoading = false;
      });
      
      // تحديث الموقع كل 10 ثواني
      _startLocationUpdates();
      
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحديد الموقع: $e';
        _isLoading = false;
      });
    }
  }
  
  void _addCurrentLocationMarker() {
    if (_currentPosition != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('current_location'),
          position: LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          infoWindow: const InfoWindow(
            title: 'موقعك الحالي',
            snippet: 'أنت هنا',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        ),
      );
    }
  }
  
  void _startLocationUpdates() {
    Timer.periodic(const Duration(seconds: 10), (timer) async {
      try {
        Position newPosition = await Geolocator.getCurrentPosition();
        setState(() {
          _currentPosition = newPosition;
          _markers.removeWhere((marker) => marker.markerId.value == 'current_location');
          _addCurrentLocationMarker();
        });
        
        // تحريك الكاميرا للموقع الجديد
        if (_mapController != null) {
          _mapController!.animateCamera(
            CameraUpdate.newLatLng(
              LatLng(newPosition.latitude, newPosition.longitude),
            ),
          );
        }
      } catch (e) {
        debugPrint('خطأ في تحديث الموقع: $e');
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageManager>(
      builder: (context, langManager, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              langManager.getText('الخريطة المباشرة', 'Live Map'),
              style: GoogleFonts.tajawal(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            backgroundColor: AppColors.primary,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.white),
            actions: [
              IconButton(
                icon: const Icon(Bootstrap.geo_alt_fill),
                onPressed: _goToCurrentLocation,
                tooltip: langManager.getText('موقعي', 'My Location'),
              ),
            ],
          ),
          body: _isLoading
              ? _buildLoadingWidget(langManager)
              : _errorMessage.isNotEmpty
                  ? _buildErrorWidget(langManager)
                  : _buildMapWidget(),
          floatingActionButton: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              FloatingActionButton(
                heroTag: "zoom_in",
                mini: true,
                onPressed: _zoomIn,
                backgroundColor: AppColors.primary,
                child: const Icon(Icons.add, color: Colors.white),
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                heroTag: "zoom_out",
                mini: true,
                onPressed: _zoomOut,
                backgroundColor: AppColors.primary,
                child: const Icon(Icons.remove, color: Colors.white),
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                heroTag: "my_location",
                mini: true,
                onPressed: _goToCurrentLocation,
                backgroundColor: AppColors.success,
                child: const Icon(Bootstrap.geo_alt_fill, color: Colors.white),
              ),
            ],
          ),
        );
      },
    );
  }
  
  Widget _buildLoadingWidget(LanguageManager langManager) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(color: AppColors.primary),
          const SizedBox(height: 16),
          Text(
            langManager.getText('جاري تحميل الخريطة...', 'Loading map...'),
            style: GoogleFonts.tajawal(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildErrorWidget(LanguageManager langManager) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Bootstrap.exclamation_triangle,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: GoogleFonts.tajawal(
                fontSize: 16,
                color: AppColors.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _errorMessage = '';
                });
                _initializeMap();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                langManager.getText('إعادة المحاولة', 'Try Again'),
                style: GoogleFonts.tajawal(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildMapWidget() {
    return GoogleMap(
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
        if (_currentPosition != null) {
          _goToCurrentLocation();
        }
      },
      initialCameraPosition: _initialPosition,
      markers: _markers,
      polylines: _polylines,
      myLocationEnabled: true,
      myLocationButtonEnabled: false,
      zoomControlsEnabled: false,
      mapType: MapType.normal,
      onTap: _onMapTapped,
    );
  }
  
  void _onMapTapped(LatLng position) {
    // إضافة marker جديد عند النقر
    setState(() {
      _markers.add(
        Marker(
          markerId: MarkerId('tapped_${DateTime.now().millisecondsSinceEpoch}'),
          position: position,
          infoWindow: InfoWindow(
            title: 'نقطة محددة',
            snippet: 'Lat: ${position.latitude.toStringAsFixed(4)}, Lng: ${position.longitude.toStringAsFixed(4)}',
          ),
        ),
      );
    });
  }
  
  void _goToCurrentLocation() {
    if (_currentPosition != null && _mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
            zoom: 16.0,
          ),
        ),
      );
    }
  }
  
  void _zoomIn() {
    if (_mapController != null) {
      _mapController!.animateCamera(CameraUpdate.zoomIn());
    }
  }
  
  void _zoomOut() {
    if (_mapController != null) {
      _mapController!.animateCamera(CameraUpdate.zoomOut());
    }
  }
  
  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
